use std::fmt;

/// Comprehensive error types for the BitBucket environment variable management application
#[derive(Debug)]
pub enum AppError {
    /// Authentication related errors
    Authentication(String),

    /// BitBucket API related errors
    Api(ApiError),

    /// File I/O related errors
    FileIo(FileIoError),

    /// Environment variable validation errors
    Validation(ValidationError),

    /// Workflow execution errors
    Workflow(WorkflowError),

    /// Conflict resolution errors
    Conflict(ConflictError),

    /// Generic errors with context
    Generic(String),
}

#[derive(Debug)]
pub enum ApiError {
    /// HTTP request failed
    RequestFailed(String),

    /// Invalid response format
    InvalidResponse(String),

    /// Resource not found (404)
    NotFound(String),

    /// Unauthorized access (401/403)
    Unauthorized(String),

    /// Rate limit exceeded
    RateLimited(String),

    /// Server error (5xx)
    ServerError(String),
}

#[derive(Debug)]
#[allow(dead_code)]
pub enum FileIoError {
    /// File not found
    FileNotFound(String),

    /// Permission denied
    PermissionDenied(String),

    /// Invalid file format
    InvalidFormat(String),

    /// File parsing error
    ParseError(String),

    /// File write error
    WriteError(String),
}

#[derive(Debug)]
#[allow(dead_code)]
pub enum ValidationError {
    /// Invalid environment variable name
    InvalidVariableName(String),

    /// Invalid environment variable value
    InvalidVariableValue(String),

    /// Missing required field
    MissingField(String),

    /// Invalid environment name
    InvalidEnvironmentName(String),

    /// Invalid repository name
    InvalidRepository(String),
}

#[derive(Debug)]
#[allow(dead_code)]
pub enum WorkflowError {
    /// Environment-to-environment workflow error
    EnvToEnv(String),

    /// File-to-new-environment workflow error
    FileToNewEnv(String),

    /// File-to-existing-environment workflow error
    FileToExistingEnv(String),

    /// Environment-to-file workflow error
    EnvToFile(String),

    /// User cancelled operation
    UserCancelled,

    /// Invalid workflow configuration
    InvalidConfiguration(String),
}

#[derive(Debug)]
#[allow(dead_code)]
pub enum ConflictError {
    /// Variable already exists
    VariableExists(String),

    /// Conflicting variable values
    ConflictingValues {
        key: String,
        existing: String,
        new: String,
    },

    /// Invalid conflict resolution strategy
    InvalidStrategy(String),

    /// User input required for conflict resolution
    UserInputRequired(String),
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::Authentication(msg) => write!(f, "Authentication error: {}", msg),
            AppError::Api(err) => write!(f, "API error: {}", err),
            AppError::FileIo(err) => write!(f, "File I/O error: {}", err),
            AppError::Validation(err) => write!(f, "Validation error: {}", err),
            AppError::Workflow(err) => write!(f, "Workflow error: {}", err),
            AppError::Conflict(err) => write!(f, "Conflict error: {}", err),
            AppError::Generic(msg) => write!(f, "Error: {}", msg),
        }
    }
}

impl fmt::Display for ApiError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ApiError::RequestFailed(msg) => write!(f, "Request failed: {}", msg),
            ApiError::InvalidResponse(msg) => write!(f, "Invalid response: {}", msg),
            ApiError::NotFound(resource) => write!(f, "Resource not found: {}", resource),
            ApiError::Unauthorized(msg) => write!(f, "Unauthorized: {}", msg),
            ApiError::RateLimited(msg) => write!(f, "Rate limited: {}", msg),
            ApiError::ServerError(msg) => write!(f, "Server error: {}", msg),
        }
    }
}

impl fmt::Display for FileIoError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            FileIoError::FileNotFound(path) => write!(f, "File not found: {}", path),
            FileIoError::PermissionDenied(path) => write!(f, "Permission denied: {}", path),
            FileIoError::InvalidFormat(msg) => write!(f, "Invalid file format: {}", msg),
            FileIoError::ParseError(msg) => write!(f, "Parse error: {}", msg),
            FileIoError::WriteError(msg) => write!(f, "Write error: {}", msg),
        }
    }
}

impl fmt::Display for ValidationError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ValidationError::InvalidVariableName(name) => {
                write!(f, "Invalid variable name: {}", name)
            }
            ValidationError::InvalidVariableValue(value) => {
                write!(f, "Invalid variable value: {}", value)
            }
            ValidationError::MissingField(field) => write!(f, "Missing required field: {}", field),
            ValidationError::InvalidEnvironmentName(name) => {
                write!(f, "Invalid environment name: {}", name)
            }
            ValidationError::InvalidRepository(repo) => write!(f, "Invalid repository: {}", repo),
        }
    }
}

impl fmt::Display for WorkflowError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            WorkflowError::EnvToEnv(msg) => write!(f, "Environment-to-environment error: {}", msg),
            WorkflowError::FileToNewEnv(msg) => write!(f, "File-to-new-environment error: {}", msg),
            WorkflowError::FileToExistingEnv(msg) => {
                write!(f, "File-to-existing-environment error: {}", msg)
            }
            WorkflowError::EnvToFile(msg) => write!(f, "Environment-to-file error: {}", msg),
            WorkflowError::UserCancelled => write!(f, "Operation cancelled by user"),
            WorkflowError::InvalidConfiguration(msg) => write!(f, "Invalid configuration: {}", msg),
        }
    }
}

impl fmt::Display for ConflictError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ConflictError::VariableExists(key) => write!(f, "Variable already exists: {}", key),
            ConflictError::ConflictingValues { key, existing, new } => {
                write!(
                    f,
                    "Conflicting values for '{}': existing='{}', new='{}'",
                    key, existing, new
                )
            }
            ConflictError::InvalidStrategy(strategy) => {
                write!(f, "Invalid conflict strategy: {}", strategy)
            }
            ConflictError::UserInputRequired(msg) => write!(f, "User input required: {}", msg),
        }
    }
}

impl std::error::Error for AppError {}
impl std::error::Error for ApiError {}
impl std::error::Error for FileIoError {}
impl std::error::Error for ValidationError {}
impl std::error::Error for WorkflowError {}
impl std::error::Error for ConflictError {}

/// Convenience type alias for Results using AppError
pub type AppResult<T> = Result<T, AppError>;

/// Convert from anyhow::Error to AppError
impl From<anyhow::Error> for AppError {
    fn from(err: anyhow::Error) -> Self {
        AppError::Generic(err.to_string())
    }
}

/// Convert from reqwest::Error to AppError
impl From<reqwest::Error> for AppError {
    fn from(err: reqwest::Error) -> Self {
        if err.is_timeout() {
            AppError::Api(ApiError::RequestFailed(format!(
                "Request timeout - please check your network connection: {}",
                err
            )))
        } else if err.is_connect() {
            AppError::Api(ApiError::RequestFailed(format!(
                "Connection failed - unable to reach BitBucket API: {}",
                err
            )))
        } else if err.is_decode() {
            AppError::Api(ApiError::InvalidResponse(format!(
                "Failed to decode response from BitBucket API: {}",
                err
            )))
        } else if let Some(status) = err.status() {
            match status.as_u16() {
                401 | 403 => AppError::Api(ApiError::Unauthorized(format!(
                    "Authentication failed - check your credentials: {}",
                    err
                ))),
                404 => AppError::Api(ApiError::NotFound(format!(
                    "Resource not found - check workspace/repository names: {}",
                    err
                ))),
                429 => AppError::Api(ApiError::RateLimited(format!(
                    "Rate limit exceeded - please wait before retrying: {}",
                    err
                ))),
                500..=599 => AppError::Api(ApiError::ServerError(format!(
                    "BitBucket server error - please try again later: {}",
                    err
                ))),
                _ => AppError::Api(ApiError::RequestFailed(format!(
                    "HTTP request failed: {}",
                    err
                ))),
            }
        } else {
            AppError::Api(ApiError::RequestFailed(format!(
                "Network request failed: {}",
                err
            )))
        }
    }
}

/// Convert from std::io::Error to AppError
impl From<std::io::Error> for AppError {
    fn from(err: std::io::Error) -> Self {
        match err.kind() {
            std::io::ErrorKind::NotFound => {
                AppError::FileIo(FileIoError::FileNotFound(err.to_string()))
            }
            std::io::ErrorKind::PermissionDenied => {
                AppError::FileIo(FileIoError::PermissionDenied(err.to_string()))
            }
            _ => AppError::FileIo(FileIoError::ParseError(err.to_string())),
        }
    }
}
