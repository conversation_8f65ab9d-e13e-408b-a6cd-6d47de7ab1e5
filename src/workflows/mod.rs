pub mod env_to_env;
pub mod file_to_existing_env;
pub mod file_to_new_env;

#[cfg(test)]
mod tests;

use crate::{args::Args, bitbucket_client::BitbucketClient, errors::AppResult};

/// Common trait for all workflow implementations
pub trait Workflow {
    /// Execute the workflow with the given arguments and client
    fn execute(&self, args: &Args, client: &BitbucketClient) -> AppResult<()>;

    /// Get the name of this workflow
    #[allow(dead_code)]
    fn name(&self) -> &'static str;

    /// Get a description of what this workflow does
    #[allow(dead_code)]
    fn description(&self) -> &'static str;
}

/// Available workflow types
#[derive(Debug, Clone, PartialEq)]
pub enum WorkflowType {
    /// Copy variables from one BitBucket environment to another
    EnvToEnv,
    /// Create a new BitBucket environment from a .env file
    FileToNewEnv,
    /// Append variables from a .env file to an existing BitBucket environment
    FileToExistingEnv,
}

impl WorkflowType {
    /// Parse workflow type from string
    #[allow(dead_code)]
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "env-to-env" | "env2env" | "copy" => Some(WorkflowType::EnvToEnv),
            "file-to-new" | "file2new" | "create" => Some(WorkflowType::FileToNewEnv),
            "file-to-existing" | "file2existing" | "append" | "merge" => {
                Some(WorkflowType::FileToExistingEnv)
            }
            _ => None,
        }
    }

    /// Get all available workflow types as strings
    #[allow(dead_code)]
    pub fn all_types() -> Vec<&'static str> {
        vec!["env-to-env", "file-to-new", "file-to-existing"]
    }

    /// Get a human-readable name for this workflow type
    pub fn display_name(&self) -> &'static str {
        match self {
            WorkflowType::EnvToEnv => "Environment to Environment",
            WorkflowType::FileToNewEnv => "File to New Environment",
            WorkflowType::FileToExistingEnv => "File to Existing Environment",
        }
    }

    /// Get a description of what this workflow does
    pub fn description(&self) -> &'static str {
        match self {
            WorkflowType::EnvToEnv => {
                "Copy environment variables from one BitBucket environment to a new environment"
            }
            WorkflowType::FileToNewEnv => {
                "Create a new BitBucket environment populated with variables from a .env file"
            }
            WorkflowType::FileToExistingEnv => {
                "Append/merge variables from a .env file into an existing BitBucket environment"
            }
        }
    }
}

/// Factory for creating workflow instances
pub struct WorkflowFactory;

impl WorkflowFactory {
    /// Create a workflow instance based on the workflow type
    pub fn create_workflow(workflow_type: WorkflowType) -> Box<dyn Workflow> {
        match workflow_type {
            WorkflowType::EnvToEnv => Box::new(env_to_env::EnvToEnvWorkflow::new()),
            WorkflowType::FileToNewEnv => Box::new(file_to_new_env::FileToNewEnvWorkflow::new()),
            WorkflowType::FileToExistingEnv => {
                Box::new(file_to_existing_env::FileToExistingEnvWorkflow::new())
            }
        }
    }

    /// Get all available workflows with their descriptions
    pub fn list_workflows() -> Vec<(WorkflowType, &'static str, &'static str)> {
        vec![
            (
                WorkflowType::EnvToEnv,
                WorkflowType::EnvToEnv.display_name(),
                WorkflowType::EnvToEnv.description(),
            ),
            (
                WorkflowType::FileToNewEnv,
                WorkflowType::FileToNewEnv.display_name(),
                WorkflowType::FileToNewEnv.description(),
            ),
            (
                WorkflowType::FileToExistingEnv,
                WorkflowType::FileToExistingEnv.display_name(),
                WorkflowType::FileToExistingEnv.description(),
            ),
        ]
    }
}
