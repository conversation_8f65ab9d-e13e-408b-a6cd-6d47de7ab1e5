use crate::{
    args::Args,
    bitbucket_client::BitbucketClient,
    env_file_parser::EnvFileParser,
    errors::{AppError, AppResult, WorkflowError},
    structs::{DeploymentEnvironment, DeploymentEnvironmentType},
    variable_operations::VariableOperations,
    workflows::Workflow,
};
use colored::Colorize;
use inquire::{required, Confirm, Select, Text};
use std::path::Path;

/// Workflow for creating a new BitBucket environment from a .env file
pub struct FileToNewEnvWorkflow;

impl FileToNewEnvWorkflow {
    pub fn new() -> Self {
        Self
    }
}

impl Workflow for FileToNewEnvWorkflow {
    fn name(&self) -> &'static str {
        "File to New Environment"
    }

    fn description(&self) -> &'static str {
        "Create a new BitBucket environment populated with variables from a .env file"
    }

    fn execute(&self, args: &Args, client: &BitbucketClient) -> AppResult<()> {
        println!("{} Starting File-to-New-Environment workflow", "🚀".green());

        // Step 1: Get .env file path
        let env_file_path = self.get_env_file_path(args)?;

        // Validate file exists
        if !Path::new(&env_file_path).exists() {
            return Err(AppError::Workflow(WorkflowError::FileToNewEnv(format!(
                "Environment file not found: {}",
                env_file_path
            ))));
        }

        // Step 2: Parse environment file
        println!(
            "{} Parsing environment file: {}",
            "📄".blue(),
            env_file_path
        );
        let parser = EnvFileParser::new();
        let parsed_variables = parser.parse_file(&env_file_path)?;

        if parsed_variables.is_empty() {
            println!(
                "{} No variables found in the environment file",
                "⚠️".yellow()
            );
            let continue_anyway = Confirm::new("Continue with empty environment?")
                .with_default(false)
                .prompt()
                .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

            if !continue_anyway {
                return Ok(());
            }
        } else {
            println!(
                "{} Found {} variables in file",
                "✅".green(),
                parsed_variables.len()
            );
        }

        // Step 3: Filter out empty values and provide user feedback
        let (mut variables, skipped_empty_variables) =
            VariableOperations::filter_empty_values(parsed_variables);

        if variables.is_empty() {
            println!(
                "{} No valid variables remaining after filtering empty values",
                "❌".red()
            );
            return Ok(());
        }

        // Step 4: Validate remaining variables
        VariableOperations::validate_variables(&variables)?;

        // Step 5: Display variables summary
        self.display_variables_summary(&variables);

        // Step 6: Allow user to review and edit variables
        variables = self.allow_variable_review(variables)?;

        // Step 7: Load repositories
        println!("{} Loading repositories...", "📂".blue());
        let repositories = client.load_repositories(&args.workspace)?;

        if repositories.is_empty() {
            return Err(AppError::Workflow(WorkflowError::FileToNewEnv(
                "No repositories found in workspace".to_string(),
            )));
        }

        // Step 8: Select target repository
        let selected_repo = Select::new("Choose target repository:", repositories)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        let repo_uuid = &selected_repo.uuid;
        let repo_name = &selected_repo.name;

        // Step 9: Get new environment details
        let target_env_types = vec!["Staging", "Production", "Test"];
        let selected_target_env_type = Select::new("Environment type:", target_env_types)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        let new_env_name = Text::new("New environment name:")
            .with_validator(required!("Environment name is required"))
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        // Step 10: Filter secured variables
        let (secured_variables, unsecured_variables) =
            VariableOperations::filter_secured_variables(&variables);

        // Display warning about secured variables
        VariableOperations::display_secured_variables_warning(&secured_variables);

        // Step 11: Confirm creation
        let total_vars = unsecured_variables.len();
        let confirmation_message =
            if secured_variables.is_empty() {
                format!(
                    "Create environment '{}' with {} variables?",
                    new_env_name, total_vars
                )
            } else {
                format!(
                "Create environment '{}' with {} variables? ({} secured variables will be skipped)",
                new_env_name, total_vars, secured_variables.len()
            )
            };

        let confirm = Confirm::new(&confirmation_message)
            .with_default(true)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        if !confirm {
            println!("{} Operation cancelled by user", "❌".yellow());
            return Ok(());
        }

        // Step 12: Create new environment
        println!(
            "{} Creating new environment '{}'...",
            "🏗️".green(),
            new_env_name
        );
        let new_environment = DeploymentEnvironment {
            deployment_gate_enabled: false,
            rank: 0,
            hidden: false,
            type_field: "deployment_environment".to_string(),
            slug: new_env_name.clone(),
            environment_type: DeploymentEnvironmentType {
                type_field: "deployment_environment_type".to_string(),
                name: selected_target_env_type.to_string(),
                rank: 1,
            },
            name: new_env_name.clone(),
            uuid: None,
        };

        let created_env =
            client.create_environment(&args.workspace, repo_uuid, &new_environment)?;
        let new_env_uuid = created_env.uuid.ok_or_else(|| {
            AppError::Workflow(WorkflowError::FileToNewEnv(
                "Created environment did not return UUID".to_string(),
            ))
        })?;

        // Step 13: Create variables in new environment
        if !unsecured_variables.is_empty() {
            println!(
                "{} Adding {} variables to new environment...",
                "📤".green(),
                unsecured_variables.len()
            );
            VariableOperations::create_variables_with_progress(
                client,
                &args.workspace,
                repo_uuid,
                &new_env_uuid,
                &unsecured_variables,
                "Creating",
            )?;
        }

        println!(
            "{} File-to-New-Environment workflow completed successfully!",
            "✅".green()
        );
        println!("  Source file: {}", env_file_path.cyan());
        println!("  Target repository: {}", repo_name.cyan());
        println!("  Target environment: {}", new_env_name.cyan());
        println!(
            "  Variables created: {}",
            unsecured_variables.len().to_string().green()
        );
        if !secured_variables.is_empty() {
            println!(
                "  Variables skipped (secured): {}",
                secured_variables.len().to_string().yellow()
            );
        }
        if !skipped_empty_variables.is_empty() {
            println!(
                "  Variables skipped (empty values): {}",
                skipped_empty_variables.len().to_string().yellow()
            );
        }

        Ok(())
    }
}

impl FileToNewEnvWorkflow {
    /// Get the environment file path from args or prompt user
    fn get_env_file_path(&self, _args: &Args) -> AppResult<String> {
        // For now, we'll prompt the user. In the future, this could come from args
        let file_path = Text::new("Path to .env file:")
            .with_default(".env")
            .with_validator(required!("File path is required"))
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        Ok(file_path)
    }

    /// Display a summary of variables found in the file
    fn display_variables_summary(&self, variables: &[crate::structs::VariableValue]) {
        if variables.is_empty() {
            return;
        }

        println!("\n{} Variables found in file:", "📋".blue());

        let (secured, unsecured): (Vec<_>, Vec<_>) = variables.iter().partition(|v| v.secured);

        if !unsecured.is_empty() {
            println!("  {} Regular variables:", "📝".green());
            for var in &unsecured {
                let value_preview = if var.value.len() > 30 {
                    format!("{}...", &var.value[..27])
                } else {
                    var.value.clone()
                };
                println!("    {} = {}", var.key.green(), value_preview);
            }
        }

        if !secured.is_empty() {
            println!("  {} Secured variables (values hidden):", "🔒".yellow());
            for var in &secured {
                println!("    {} = <secured>", var.key.yellow());
            }
        }
        println!();
    }

    /// Allow user to review and edit variables before creating environment
    fn allow_variable_review(
        &self,
        mut variables: Vec<crate::structs::VariableValue>,
    ) -> AppResult<Vec<crate::structs::VariableValue>> {
        if variables.is_empty() {
            return Ok(variables);
        }

        let review = Confirm::new("Review and edit variables before creating environment?")
            .with_default(false)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        if !review {
            return Ok(variables);
        }

        loop {
            let options = vec![
                "Edit a variable",
                "Remove a variable",
                "Add a new variable",
                "Finish review",
            ];

            let action = Select::new("What would you like to do?", options)
                .prompt()
                .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

            match action {
                "Edit a variable" => {
                    if variables.is_empty() {
                        println!("{} No variables to edit", "⚠️".yellow());
                        continue;
                    }

                    let selected_var = Select::new("Choose variable to edit:", variables.clone())
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    let new_value = Text::new("New value:")
                        .with_default(&selected_var.value)
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    // Update the variable
                    for var in &mut variables {
                        if var.key == selected_var.key {
                            var.value = new_value.clone();
                            break;
                        }
                    }

                    println!("{} Variable '{}' updated", "✅".green(), selected_var.key);
                }
                "Remove a variable" => {
                    if variables.is_empty() {
                        println!("{} No variables to remove", "⚠️".yellow());
                        continue;
                    }

                    let selected_var = Select::new("Choose variable to remove:", variables.clone())
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    variables.retain(|v| v.key != selected_var.key);
                    println!("{} Variable '{}' removed", "🗑️".red(), selected_var.key);
                }
                "Add a new variable" => {
                    let key = Text::new("Variable name:")
                        .with_validator(required!("Variable name is required"))
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    let value = Text::new("Variable value:")
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    let new_var = crate::structs::VariableValue {
                        type_field: "deployment_variable".to_string(),
                        uuid: String::new(),
                        key: key.clone(),
                        value,
                        secured: EnvFileParser::is_sensitive_variable(&key),
                    };

                    variables.push(new_var);
                    println!("{} Variable '{}' added", "➕".green(), key);
                }
                "Finish review" => break,
                _ => unreachable!(),
            }
        }

        Ok(variables)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_workflow_name() {
        let workflow = FileToNewEnvWorkflow::new();
        assert_eq!(workflow.name(), "File to New Environment");
    }

    #[test]
    fn test_workflow_description() {
        let workflow = FileToNewEnvWorkflow::new();
        assert!(!workflow.description().is_empty());
    }
}
