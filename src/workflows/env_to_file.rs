use crate::{
    args::{Args, WorkflowCommand},
    bitbucket_client::BitbucketClient,
    errors::{AppError, AppResult, WorkflowError},
    structs::VariableValue,
    workflows::Workflow,
};
use colored::Colorize;
use inquire::{required, Confirm, Select, Text};
use std::fs::File;
use std::io::Write;
use std::path::Path;

/// Workflow for exporting environment variables from a BitBucket environment to a .env file
pub struct EnvToFileWorkflow;

impl EnvToFileWorkflow {
    pub fn new() -> Self {
        Self
    }

    /// Get the output file path from args or prompt user
    fn get_output_file_path(&self, args: &Args) -> AppResult<String> {
        // Check if file path was provided via command line
        if let Some(WorkflowCommand::EnvToFile { file }) = &args.workflow {
            if let Some(file_path) = file {
                return Ok(file_path.clone());
            }
        }

        // Prompt user for file path
        let file_path = Text::new("Output .env file path:")
            .with_default(".env")
            .with_validator(required!("File path is required"))
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        Ok(file_path)
    }

    /// Check if file exists and confirm overwrite
    fn confirm_file_overwrite(&self, file_path: &str) -> AppResult<bool> {
        if Path::new(file_path).exists() {
            let confirm = Confirm::new(&format!("File '{}' already exists. Overwrite?", file_path))
                .with_default(false)
                .prompt()
                .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;
            Ok(confirm)
        } else {
            Ok(true)
        }
    }

    /// Generate .env file content with proper formatting
    fn generate_env_file_content(
        &self,
        variables: &[VariableValue],
        skipped_secured: &[VariableValue],
        workspace: &str,
        repo_name: &str,
        env_name: &str,
    ) -> String {
        let mut content = String::new();

        // Header comment with source information
        content.push_str(&format!(
            "# Environment variables exported from BitBucket\n"
        ));
        content.push_str(&format!("# Workspace: {}\n", workspace));
        content.push_str(&format!("# Repository: {}\n", repo_name));
        content.push_str(&format!("# Environment: {}\n", env_name));
        content.push_str(&format!(
            "# Export timestamp: {}\n",
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs()
        ));
        content.push_str(&format!("# Total variables: {}\n", variables.len()));
        if !skipped_secured.is_empty() {
            content.push_str(&format!(
                "# Secured variables skipped: {}\n",
                skipped_secured.len()
            ));
        }
        content.push_str("#\n");
        content.push_str("# WARNING: This file may contain sensitive information.\n");
        content.push_str("# Do not commit this file to version control.\n");
        content.push_str("\n");

        // Add skipped secured variables as comments
        if !skipped_secured.is_empty() {
            content.push_str("# The following secured variables were skipped during export:\n");
            for var in skipped_secured {
                content.push_str(&format!(
                    "# {} (secured variable - value not exported)\n",
                    var.key
                ));
            }
            content.push_str("\n");
        }

        // Add exported variables
        for var in variables {
            let value = self.escape_env_value(&var.value);
            content.push_str(&format!("{}={}\n", var.key, value));
        }

        content
    }

    /// Escape and quote environment variable values as needed
    fn escape_env_value(&self, value: &str) -> String {
        // If value is empty, return empty string
        if value.is_empty() {
            return String::new();
        }

        // If value contains spaces, quotes, or special characters, wrap in double quotes
        if value.contains(' ')
            || value.contains('\t')
            || value.contains('\n')
            || value.contains('"')
            || value.contains('\'')
            || value.contains('\\')
            || value.contains('$')
            || value.contains('`')
        {
            // Escape double quotes and backslashes
            let escaped = value.replace('\\', "\\\\").replace('"', "\\\"");
            format!("\"{}\"", escaped)
        } else {
            value.to_string()
        }
    }

    /// Write content to file
    fn write_env_file(&self, file_path: &str, content: &str) -> AppResult<()> {
        let mut file = File::create(file_path).map_err(|e| {
            AppError::Workflow(WorkflowError::EnvToFile(format!(
                "Failed to create file '{}': {}",
                file_path, e
            )))
        })?;

        file.write_all(content.as_bytes()).map_err(|e| {
            AppError::Workflow(WorkflowError::EnvToFile(format!(
                "Failed to write to file '{}': {}",
                file_path, e
            )))
        })?;

        file.flush().map_err(|e| {
            AppError::Workflow(WorkflowError::EnvToFile(format!(
                "Failed to flush file '{}': {}",
                file_path, e
            )))
        })?;

        Ok(())
    }
}

impl Workflow for EnvToFileWorkflow {
    fn name(&self) -> &'static str {
        "Environment to File"
    }

    fn description(&self) -> &'static str {
        "Export environment variables from a BitBucket environment to a .env file"
    }

    fn execute(&self, args: &Args, client: &BitbucketClient) -> AppResult<()> {
        println!("{} Starting Environment-to-File workflow", "🚀".green());

        // Step 1: Load repositories
        println!("{} Loading repositories...", "📂".blue());
        let repositories = client.load_repositories(&args.workspace)?;

        if repositories.is_empty() {
            return Err(AppError::Workflow(WorkflowError::EnvToFile(
                "No repositories found in workspace".to_string(),
            )));
        }

        // Step 2: Select source repository
        let selected_repo = Select::new("Choose repository:", repositories)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        let repo_uuid = &selected_repo.uuid;
        let repo_name = &selected_repo.name;

        // Step 3: Load environments for the selected repository
        println!(
            "{} Loading environments for repository '{}'...",
            "🌍".blue(),
            repo_name
        );
        let environments = client.load_environments(&args.workspace, repo_uuid)?;

        if environments.is_empty() {
            return Err(AppError::Workflow(WorkflowError::EnvToFile(format!(
                "No environments found in repository '{}'",
                repo_name
            ))));
        }

        // Step 4: Select source environment
        let selected_env = Select::new("Choose environment to export from:", environments)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        // Step 5: Load variables from source environment
        println!(
            "{} Loading variables from environment '{}'...",
            "📋".blue(),
            selected_env.name
        );
        let env_variables =
            client.load_environment_variables(&args.workspace, repo_uuid, &selected_env.uuid)?;

        if env_variables.is_empty() {
            println!(
                "{} No variables found in environment '{}'",
                "⚠️".yellow(),
                selected_env.name
            );
            return Ok(());
        }

        println!(
            "{} Found {} variables in environment '{}'",
            "📊".blue(),
            env_variables.len(),
            selected_env.name
        );

        // Step 6: Separate secured and unsecured variables
        let (unsecured_variables, secured_variables): (Vec<_>, Vec<_>) =
            env_variables.into_iter().partition(|var| !var.secured);

        // Step 7: Get output file path
        let output_file_path = self.get_output_file_path(args)?;

        // Step 8: Check if file exists and confirm overwrite
        if !self.confirm_file_overwrite(&output_file_path)? {
            println!("{} Operation cancelled by user", "❌".yellow());
            return Ok(());
        }

        // Step 9: Generate file content
        let file_content = self.generate_env_file_content(
            &unsecured_variables,
            &secured_variables,
            &args.workspace,
            repo_name,
            &selected_env.name,
        );

        // Step 10: Write to file
        println!(
            "{} Writing variables to file '{}'...",
            "💾".green(),
            output_file_path
        );
        self.write_env_file(&output_file_path, &file_content)?;

        // Step 11: Display success summary
        println!(
            "{} Environment-to-File workflow completed successfully!",
            "✅".green()
        );
        println!("  Source workspace: {}", args.workspace.cyan());
        println!("  Source repository: {}", repo_name.cyan());
        println!("  Source environment: {}", selected_env.name.cyan());
        println!("  Output file: {}", output_file_path.cyan());
        println!(
            "  Variables exported: {}",
            unsecured_variables.len().to_string().green()
        );
        if !secured_variables.is_empty() {
            println!(
                "  Variables skipped (secured): {}",
                secured_variables.len().to_string().yellow()
            );
            println!(
                "  {} Secured variables are listed as comments in the output file",
                "ℹ️".blue()
            );
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::structs::VariableValue;
    use std::fs;
    use tempfile::NamedTempFile;

    fn create_test_variable(key: &str, value: &str, secured: bool) -> VariableValue {
        VariableValue {
            type_field: "deployment_variable".to_string(),
            uuid: format!("uuid-{}", key),
            key: key.to_string(),
            value: value.to_string(),
            secured,
        }
    }

    #[test]
    fn test_workflow_name() {
        let workflow = EnvToFileWorkflow::new();
        assert_eq!(workflow.name(), "Environment to File");
    }

    #[test]
    fn test_workflow_description() {
        let workflow = EnvToFileWorkflow::new();
        assert_eq!(
            workflow.description(),
            "Export environment variables from a BitBucket environment to a .env file"
        );
    }

    #[test]
    fn test_escape_env_value_simple() {
        let workflow = EnvToFileWorkflow::new();
        assert_eq!(workflow.escape_env_value("simple_value"), "simple_value");
        assert_eq!(workflow.escape_env_value("123"), "123");
        assert_eq!(workflow.escape_env_value(""), "");
    }

    #[test]
    fn test_escape_env_value_with_spaces() {
        let workflow = EnvToFileWorkflow::new();
        assert_eq!(
            workflow.escape_env_value("value with spaces"),
            "\"value with spaces\""
        );
    }

    #[test]
    fn test_escape_env_value_with_quotes() {
        let workflow = EnvToFileWorkflow::new();
        assert_eq!(
            workflow.escape_env_value("value with \"quotes\""),
            "\"value with \\\"quotes\\\"\""
        );
    }

    #[test]
    fn test_escape_env_value_with_backslashes() {
        let workflow = EnvToFileWorkflow::new();
        assert_eq!(
            workflow.escape_env_value("path\\to\\file"),
            "\"path\\\\to\\\\file\""
        );
    }

    #[test]
    fn test_escape_env_value_with_special_chars() {
        let workflow = EnvToFileWorkflow::new();
        assert_eq!(
            workflow.escape_env_value("value$with`special"),
            "\"value$with`special\""
        );
        assert_eq!(
            workflow.escape_env_value("value\twith\ttabs"),
            "\"value\twith\ttabs\""
        );
        assert_eq!(
            workflow.escape_env_value("value\nwith\nnewlines"),
            "\"value\nwith\nnewlines\""
        );
    }

    #[test]
    fn test_generate_env_file_content() {
        let workflow = EnvToFileWorkflow::new();

        let variables = vec![
            create_test_variable("VAR1", "value1", false),
            create_test_variable("VAR2", "value with spaces", false),
            create_test_variable("VAR3", "", false),
        ];

        let secured_variables = vec![create_test_variable("SECRET_KEY", "secret_value", true)];

        let content = workflow.generate_env_file_content(
            &variables,
            &secured_variables,
            "test-workspace",
            "test-repo",
            "test-env",
        );

        // Check header comments
        assert!(content.contains("# Environment variables exported from BitBucket"));
        assert!(content.contains("# Workspace: test-workspace"));
        assert!(content.contains("# Repository: test-repo"));
        assert!(content.contains("# Environment: test-env"));
        assert!(content.contains("# Total variables: 3"));
        assert!(content.contains("# Secured variables skipped: 1"));

        // Check secured variable comments
        assert!(content.contains("# SECRET_KEY (secured variable - value not exported)"));

        // Check exported variables
        assert!(content.contains("VAR1=value1"));
        assert!(content.contains("VAR2=\"value with spaces\""));
        assert!(content.contains("VAR3="));

        // Check warnings
        assert!(content.contains("# WARNING: This file may contain sensitive information."));
        assert!(content.contains("# Do not commit this file to version control."));
    }

    #[test]
    fn test_generate_env_file_content_no_secured_vars() {
        let workflow = EnvToFileWorkflow::new();

        let variables = vec![create_test_variable("VAR1", "value1", false)];

        let secured_variables = vec![];

        let content = workflow.generate_env_file_content(
            &variables,
            &secured_variables,
            "test-workspace",
            "test-repo",
            "test-env",
        );

        // Should not contain secured variables section
        assert!(!content.contains("# Secured variables skipped:"));
        assert!(!content.contains("# The following secured variables were skipped"));

        // Should contain the variable
        assert!(content.contains("VAR1=value1"));
    }

    #[test]
    fn test_write_env_file() {
        let workflow = EnvToFileWorkflow::new();
        let temp_file = NamedTempFile::new().unwrap();
        let file_path = temp_file.path().to_str().unwrap();

        let content = "# Test content\nVAR1=value1\nVAR2=value2\n";

        // Write content to file
        workflow.write_env_file(file_path, content).unwrap();

        // Read back and verify
        let written_content = fs::read_to_string(file_path).unwrap();
        assert_eq!(written_content, content);
    }

    #[test]
    fn test_write_env_file_invalid_path() {
        let workflow = EnvToFileWorkflow::new();
        let invalid_path = "/invalid/path/that/does/not/exist/file.env";

        let content = "VAR1=value1\n";

        // Should return an error
        let result = workflow.write_env_file(invalid_path, content);
        assert!(result.is_err());

        if let Err(AppError::Workflow(WorkflowError::EnvToFile(msg))) = result {
            assert!(msg.contains("Failed to create file"));
        } else {
            panic!("Expected EnvToFile error");
        }
    }
}
