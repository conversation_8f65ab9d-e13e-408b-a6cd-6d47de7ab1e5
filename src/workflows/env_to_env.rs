use crate::{
    args::Args,
    bitbucket_client::BitbucketClient,
    errors::{AppError, AppR<PERSON>ult, WorkflowError},
    structs::{DeploymentEnvironment, DeploymentEnvironmentType, VariableValue},
    variable_operations::VariableOperations,
    workflows::Workflow,
};
use colored::Colorize;
use inquire::{required, Confirm, Select, Text};

/// Workflow for copying environment variables from one BitBucket environment to another
pub struct EnvToEnvWorkflow;

impl EnvToEnvWorkflow {
    pub fn new() -> Self {
        Self
    }
}

impl Workflow for EnvToEnvWorkflow {
    fn name(&self) -> &'static str {
        "Environment to Environment"
    }

    fn description(&self) -> &'static str {
        "Copy environment variables from one BitBucket environment to a new environment"
    }

    fn execute(&self, args: &Args, client: &BitbucketClient) -> AppResult<()> {
        println!(
            "{} Starting Environment-to-Environment workflow",
            "🚀".green()
        );

        // Step 1: Load repositories
        println!("{} Loading repositories...", "📂".blue());
        let repositories = client.load_repositories(&args.workspace)?;

        if repositories.is_empty() {
            return Err(AppError::Workflow(WorkflowError::EnvToEnv(
                "No repositories found in workspace".to_string(),
            )));
        }

        // Step 2: Select source repository
        let selected_repo = Select::new("Choose repository:", repositories)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        let repo_slug = selected_repo.name;

        // Step 3: Load environments for the selected repository
        println!(
            "{} Loading environments for repository '{}'...",
            "🌍".blue(),
            repo_slug
        );
        let environments = client.load_environments(&args.workspace, &repo_slug)?;

        if environments.is_empty() {
            return Err(AppError::Workflow(WorkflowError::EnvToEnv(format!(
                "No environments found in repository '{}'",
                repo_slug
            ))));
        }

        // Step 4: Select source environment
        let selected_env = Select::new("Choose environment to copy from:", environments)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        // Step 5: Load variables from source environment
        println!(
            "{} Loading variables from environment '{}'...",
            "📋".blue(),
            selected_env.name
        );
        let mut env_variables =
            client.load_environment_variables(&args.workspace, &repo_slug, &selected_env.uuid)?;

        if env_variables.is_empty() {
            println!(
                "{} No variables found in the selected environment",
                "⚠️".yellow()
            );
            let continue_anyway = Confirm::new("Continue with empty environment?")
                .with_default(false)
                .prompt()
                .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

            if !continue_anyway {
                return Ok(());
            }
        }

        // Step 6: Allow user to edit variables
        env_variables = self.allow_variable_editing(env_variables)?;

        // Step 7: Get target environment details
        let target_env_types = vec!["Staging", "Production", "Test"];
        let selected_target_env_type = Select::new("Target environment type:", target_env_types)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        let new_env_name = Text::new("New environment name:")
            .with_validator(required!("Environment name is required"))
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        // Step 8: Filter out empty values and provide user feedback
        let (filtered_variables, skipped_empty_variables) =
            VariableOperations::filter_empty_values(env_variables);

        if filtered_variables.is_empty() {
            println!(
                "{} No valid variables remaining after filtering empty values",
                "❌".red()
            );
            return Ok(());
        }

        // Step 9: Validate remaining variables
        VariableOperations::validate_variables(&filtered_variables)?;

        // Step 10: Filter secured variables
        let (secured_variables, unsecured_variables) =
            VariableOperations::filter_secured_variables(&filtered_variables);

        // Display warning about secured variables
        VariableOperations::display_secured_variables_warning(&secured_variables);

        if unsecured_variables.is_empty() && !secured_variables.is_empty() {
            println!(
                "{} All variables are secured and cannot be copied",
                "❌".red()
            );
            return Ok(());
        }

        // Step 11: Create new environment
        println!(
            "{} Creating new environment '{}'...",
            "🏗️".green(),
            new_env_name
        );
        let new_environment = DeploymentEnvironment {
            deployment_gate_enabled: false,
            rank: 0,
            hidden: false,
            type_field: "deployment_environment".to_string(),
            slug: new_env_name.clone(),
            environment_type: DeploymentEnvironmentType {
                type_field: "deployment_environment_type".to_string(),
                name: selected_target_env_type.to_string(),
                rank: 1,
            },
            name: new_env_name.clone(),
            uuid: None,
        };

        let created_env =
            client.create_environment(&args.workspace, &repo_slug, &new_environment)?;
        let new_env_uuid = created_env.uuid.ok_or_else(|| {
            AppError::Workflow(WorkflowError::EnvToEnv(
                "Created environment did not return UUID".to_string(),
            ))
        })?;

        // Step 12: Copy variables to new environment
        if !unsecured_variables.is_empty() {
            println!(
                "{} Copying {} variables to new environment...",
                "📤".green(),
                unsecured_variables.len()
            );
            VariableOperations::create_variables_with_progress(
                client,
                &args.workspace,
                &repo_slug,
                &new_env_uuid,
                &unsecured_variables,
                "Copying",
            )?;
        }

        println!(
            "{} Environment-to-Environment workflow completed successfully!",
            "✅".green()
        );
        println!("  Source environment: {}", selected_env.name.cyan());
        println!("  Target environment: {}", new_env_name.cyan());
        println!(
            "  Variables copied: {}",
            unsecured_variables.len().to_string().green()
        );
        if !secured_variables.is_empty() {
            println!(
                "  Variables skipped (secured): {}",
                secured_variables.len().to_string().yellow()
            );
        }
        if !skipped_empty_variables.is_empty() {
            println!(
                "  Variables skipped (empty values): {}",
                skipped_empty_variables.len().to_string().yellow()
            );
        }

        Ok(())
    }
}

impl EnvToEnvWorkflow {
    /// Allow user to interactively edit variables before copying
    fn allow_variable_editing(
        &self,
        mut variables: Vec<VariableValue>,
    ) -> AppResult<Vec<VariableValue>> {
        if variables.is_empty() {
            return Ok(variables);
        }

        loop {
            let continue_edit = Confirm::new("Edit any variables before copying?")
                .with_default(false)
                .prompt()
                .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

            if !continue_edit {
                break;
            }

            let selected_var = Select::new("Choose variable to edit:", variables.clone())
                .prompt()
                .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

            let new_value = Text::new("New value:")
                .with_default(&selected_var.value)
                .prompt()
                .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

            // Update the variable in the list
            variables = variables
                .into_iter()
                .map(|mut v| {
                    if v.uuid == selected_var.uuid {
                        v.value = new_value.clone();
                    }
                    v
                })
                .collect();

            println!("{} Variable '{}' updated", "✅".green(), selected_var.key);
        }

        Ok(variables)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_workflow_name() {
        let workflow = EnvToEnvWorkflow::new();
        assert_eq!(workflow.name(), "Environment to Environment");
    }

    #[test]
    fn test_workflow_description() {
        let workflow = EnvToEnvWorkflow::new();
        assert!(!workflow.description().is_empty());
    }
}
