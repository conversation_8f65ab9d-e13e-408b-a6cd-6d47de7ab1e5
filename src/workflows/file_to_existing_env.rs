use crate::{
    args::Args,
    bitbucket_client::BitbucketClient,
    conflict_resolver::{ConflictResolver, ConflictStrategy},
    env_file_parser::EnvFileParser,
    errors::{AppError, AppResult, WorkflowError},
    variable_operations::VariableOperations,
    workflows::Workflow,
};
use colored::Colorize;
use inquire::{required, Confirm, Select, Text};
use std::path::Path;

/// Workflow for appending variables from a .env file to an existing BitBucket environment
pub struct FileToExistingEnvWorkflow;

impl FileToExistingEnvWorkflow {
    pub fn new() -> Self {
        Self
    }
}

impl Workflow for FileToExistingEnvWorkflow {
    fn name(&self) -> &'static str {
        "File to Existing Environment"
    }

    fn description(&self) -> &'static str {
        "Append/merge variables from a .env file into an existing BitBucket environment"
    }

    fn execute(&self, args: &Args, client: &BitbucketClient) -> AppResult<()> {
        println!(
            "{} Starting File-to-Existing-Environment workflow",
            "🚀".green()
        );

        // Step 1: Get .env file path
        let env_file_path = self.get_env_file_path(args)?;

        // Validate file exists
        if !Path::new(&env_file_path).exists() {
            return Err(AppError::Workflow(WorkflowError::FileToExistingEnv(
                format!("Environment file not found: {}", env_file_path),
            )));
        }

        // Step 2: Parse environment file
        println!(
            "{} Parsing environment file: {}",
            "📄".blue(),
            env_file_path
        );
        let parser = EnvFileParser::new();
        let parsed_variables = parser.parse_file(&env_file_path)?;

        if parsed_variables.is_empty() {
            println!(
                "{} No variables found in the environment file",
                "⚠️".yellow()
            );
            return Ok(());
        }

        println!(
            "{} Found {} variables in file",
            "✅".green(),
            parsed_variables.len()
        );

        // Step 3: Filter out empty values and provide user feedback
        let (mut new_variables, skipped_empty_variables) =
            VariableOperations::filter_empty_values(parsed_variables);

        if new_variables.is_empty() {
            println!(
                "{} No valid variables remaining after filtering empty values",
                "❌".red()
            );
            return Ok(());
        }

        // Step 4: Validate remaining variables
        VariableOperations::validate_variables(&new_variables)?;

        // Step 5: Display variables summary
        self.display_variables_summary(&new_variables);

        // Step 6: Allow user to review and edit variables
        new_variables = self.allow_variable_review(new_variables)?;

        // Step 7: Load repositories
        println!("{} Loading repositories...", "📂".blue());
        let repositories = client.load_repositories(&args.workspace)?;

        if repositories.is_empty() {
            return Err(AppError::Workflow(WorkflowError::FileToExistingEnv(
                "No repositories found in workspace".to_string(),
            )));
        }

        // Step 8: Select target repository
        let selected_repo = Select::new("Choose target repository:", repositories)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        let repo_slug = selected_repo.name;

        // Step 9: Load environments for the selected repository
        println!(
            "{} Loading environments for repository '{}'...",
            "🌍".blue(),
            repo_slug
        );
        let environments = client.load_environments(&args.workspace, &repo_slug)?;

        if environments.is_empty() {
            return Err(AppError::Workflow(WorkflowError::FileToExistingEnv(
                format!("No environments found in repository '{}'", repo_slug),
            )));
        }

        // Step 10: Select target environment
        let selected_env = Select::new("Choose target environment:", environments)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        // Step 11: Load existing variables from target environment
        println!(
            "{} Loading existing variables from environment '{}'...",
            "📋".blue(),
            selected_env.name
        );
        let existing_variables =
            client.load_environment_variables(&args.workspace, &repo_slug, &selected_env.uuid)?;

        println!(
            "{} Found {} existing variables in target environment",
            "📊".blue(),
            existing_variables.len()
        );

        // Step 12: Get conflict resolution strategy
        let conflict_strategy = self.get_conflict_strategy()?;

        // Step 13: Resolve conflicts
        println!("{} Analyzing conflicts...", "🔍".blue());
        let resolver = ConflictResolver::new(conflict_strategy, true);
        let resolution = resolver.resolve_conflicts(&existing_variables, &new_variables)?;

        // Step 14: Display resolution summary
        resolution.display_summary();

        if !resolution.has_changes() {
            println!("{} No changes to apply", "ℹ️".blue());
            return Ok(());
        }

        // Step 15: Confirm changes
        let confirmation_message = format!(
            "Apply {} changes to environment '{}'?",
            resolution.total_changes(),
            selected_env.name
        );

        let confirm = Confirm::new(&confirmation_message)
            .with_default(true)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        if !confirm {
            println!("{} Operation cancelled by user", "❌".yellow());
            return Ok(());
        }

        // Step 16: Apply changes
        let mut changes_applied = 0;

        // Add new variables
        if !resolution.to_add.is_empty() {
            println!(
                "{} Adding {} new variables...",
                "➕".green(),
                resolution.to_add.len()
            );
            VariableOperations::create_variables_with_progress(
                client,
                &args.workspace,
                &repo_slug,
                &selected_env.uuid,
                &resolution.to_add,
                "Adding",
            )?;
            changes_applied += resolution.to_add.len();
        }

        // Update existing variables
        if !resolution.to_update.is_empty() {
            println!(
                "{} Updating {} existing variables...",
                "🔄".yellow(),
                resolution.to_update.len()
            );
            VariableOperations::update_variables_with_progress(
                client,
                &args.workspace,
                &repo_slug,
                &selected_env.uuid,
                &resolution.to_update,
                "Updating",
            )?;
            changes_applied += resolution.to_update.len();
        }

        println!(
            "{} File-to-Existing-Environment workflow completed successfully!",
            "✅".green()
        );
        println!("  Source file: {}", env_file_path.cyan());
        println!("  Target repository: {}", repo_slug.cyan());
        println!("  Target environment: {}", selected_env.name.cyan());
        println!("  Changes applied: {}", changes_applied.to_string().green());
        if !resolution.skipped.is_empty() {
            println!(
                "  Variables skipped (conflicts): {}",
                resolution.skipped.len().to_string().yellow()
            );
        }
        if !skipped_empty_variables.is_empty() {
            println!(
                "  Variables skipped (empty values): {}",
                skipped_empty_variables.len().to_string().yellow()
            );
        }

        Ok(())
    }
}

impl FileToExistingEnvWorkflow {
    /// Get the environment file path from args or prompt user
    fn get_env_file_path(&self, _args: &Args) -> AppResult<String> {
        // For now, we'll prompt the user. In the future, this could come from args
        let file_path = Text::new("Path to .env file:")
            .with_default(".env")
            .with_validator(required!("File path is required"))
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        Ok(file_path)
    }

    /// Get conflict resolution strategy from user
    fn get_conflict_strategy(&self) -> AppResult<ConflictStrategy> {
        let strategies = vec![
            ("prompt", "Prompt for each conflict (recommended)"),
            ("overwrite", "Overwrite existing values with new ones"),
            ("skip", "Skip conflicting variables, keep existing values"),
            ("merge", "Merge values (concatenate with semicolon)"),
        ];

        let strategy_descriptions: Vec<String> = strategies
            .iter()
            .map(|(name, desc)| format!("{}: {}", name, desc))
            .collect();

        let selected = Select::new(
            "Choose conflict resolution strategy:",
            strategy_descriptions,
        )
        .prompt()
        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        // Extract strategy name from selection
        let strategy_name = selected.split(':').next().ok_or_else(|| {
            AppError::Workflow(WorkflowError::FileToExistingEnv(
                "Invalid strategy selection format".to_string(),
            ))
        })?;
        ConflictStrategy::from_str(strategy_name)
    }

    /// Display a summary of variables found in the file
    fn display_variables_summary(&self, variables: &[crate::structs::VariableValue]) {
        if variables.is_empty() {
            return;
        }

        println!("\n{} Variables to merge:", "📋".blue());

        let (secured, unsecured): (Vec<_>, Vec<_>) = variables.iter().partition(|v| v.secured);

        if !unsecured.is_empty() {
            println!("  {} Regular variables:", "📝".green());
            for var in &unsecured {
                let value_preview = if var.value.len() > 30 {
                    format!("{}...", &var.value[..27])
                } else {
                    var.value.clone()
                };
                println!("    {} = {}", var.key.green(), value_preview);
            }
        }

        if !secured.is_empty() {
            println!("  {} Secured variables (values hidden):", "🔒".yellow());
            for var in &secured {
                println!("    {} = <secured>", var.key.yellow());
            }
        }
        println!();
    }

    /// Allow user to review and edit variables before merging
    fn allow_variable_review(
        &self,
        mut variables: Vec<crate::structs::VariableValue>,
    ) -> AppResult<Vec<crate::structs::VariableValue>> {
        if variables.is_empty() {
            return Ok(variables);
        }

        let review = Confirm::new("Review and edit variables before merging?")
            .with_default(false)
            .prompt()
            .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

        if !review {
            return Ok(variables);
        }

        loop {
            let options = vec![
                "Edit a variable",
                "Remove a variable",
                "Add a new variable",
                "Finish review",
            ];

            let action = Select::new("What would you like to do?", options)
                .prompt()
                .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

            match action {
                "Edit a variable" => {
                    if variables.is_empty() {
                        println!("{} No variables to edit", "⚠️".yellow());
                        continue;
                    }

                    let selected_var = Select::new("Choose variable to edit:", variables.clone())
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    let new_value = Text::new("New value:")
                        .with_default(&selected_var.value)
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    // Update the variable
                    for var in &mut variables {
                        if var.key == selected_var.key {
                            var.value = new_value.clone();
                            break;
                        }
                    }

                    println!("{} Variable '{}' updated", "✅".green(), selected_var.key);
                }
                "Remove a variable" => {
                    if variables.is_empty() {
                        println!("{} No variables to remove", "⚠️".yellow());
                        continue;
                    }

                    let selected_var = Select::new("Choose variable to remove:", variables.clone())
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    variables.retain(|v| v.key != selected_var.key);
                    println!("{} Variable '{}' removed", "🗑️".red(), selected_var.key);
                }
                "Add a new variable" => {
                    let key = Text::new("Variable name:")
                        .with_validator(required!("Variable name is required"))
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    let value = Text::new("Variable value:")
                        .prompt()
                        .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                    let new_var = crate::structs::VariableValue {
                        type_field: "deployment_variable".to_string(),
                        uuid: String::new(),
                        key: key.clone(),
                        value,
                        secured: EnvFileParser::is_sensitive_variable(&key),
                    };

                    variables.push(new_var);
                    println!("{} Variable '{}' added", "➕".green(), key);
                }
                "Finish review" => break,
                _ => unreachable!(),
            }
        }

        Ok(variables)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_workflow_name() {
        let workflow = FileToExistingEnvWorkflow::new();
        assert_eq!(workflow.name(), "File to Existing Environment");
    }

    #[test]
    fn test_workflow_description() {
        let workflow = FileToExistingEnvWorkflow::new();
        assert!(!workflow.description().is_empty());
    }
}
