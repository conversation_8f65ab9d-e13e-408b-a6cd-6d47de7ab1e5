#[cfg(test)]
mod workflow_tests {
    use crate::{
        args::{Args, WorkflowCommand},
        auth::BitbucketCredentials,
        bitbucket_client::BitbucketClient,
        workflows::{WorkflowFactory, WorkflowType},
    };
    use std::io::Write;
    use tempfile::NamedTempFile;

    fn create_test_args_env_to_env() -> Args {
        Args {
            username: Some("<EMAIL>".to_string()),
            api_token: Some("test_token".to_string()),
            workspace: "test-workspace".to_string(),
            verbose: false,
            workflow: Some(WorkflowCommand::EnvToEnv),
        }
    }

    fn create_test_args_file_to_new(file_path: Option<String>) -> Args {
        Args {
            username: Some("<EMAIL>".to_string()),
            api_token: Some("test_token".to_string()),
            workspace: "test-workspace".to_string(),
            verbose: false,
            workflow: Some(WorkflowCommand::FileToNew { file: file_path }),
        }
    }

    fn create_test_args_file_to_existing(file_path: Option<String>, strategy: String) -> Args {
        Args {
            username: Some("<EMAIL>".to_string()),
            api_token: Some("test_token".to_string()),
            workspace: "test-workspace".to_string(),
            verbose: false,
            workflow: Some(WorkflowCommand::FileToExisting {
                file: file_path,
                conflict_strategy: strategy,
            }),
        }
    }

    #[allow(dead_code)]
    fn create_test_client() -> BitbucketClient {
        let credentials =
            BitbucketCredentials::new("<EMAIL>".to_string(), "test_token".to_string());
        BitbucketClient::new(credentials, false)
    }

    fn create_test_env_file() -> NamedTempFile {
        let mut temp_file = NamedTempFile::new().unwrap();
        writeln!(temp_file, "# Test environment file").unwrap();
        writeln!(temp_file, "VAR1=value1").unwrap();
        writeln!(temp_file, "VAR2=value2").unwrap();
        writeln!(temp_file, "SECRET_KEY=secret123").unwrap();
        temp_file.flush().unwrap();
        temp_file
    }

    #[test]
    fn test_workflow_factory_create_env_to_env() {
        let workflow = WorkflowFactory::create_workflow(WorkflowType::EnvToEnv);
        assert_eq!(workflow.name(), "Environment to Environment");
        assert_eq!(
            workflow.description(),
            "Copy environment variables from one BitBucket environment to a new environment"
        );
    }

    #[test]
    fn test_workflow_factory_create_file_to_new() {
        let workflow = WorkflowFactory::create_workflow(WorkflowType::FileToNewEnv);
        assert_eq!(workflow.name(), "File to New Environment");
        assert_eq!(
            workflow.description(),
            "Create a new BitBucket environment populated with variables from a .env file"
        );
    }

    #[test]
    fn test_workflow_factory_create_file_to_existing() {
        let workflow = WorkflowFactory::create_workflow(WorkflowType::FileToExistingEnv);
        assert_eq!(workflow.name(), "File to Existing Environment");
        assert_eq!(
            workflow.description(),
            "Append/merge variables from a .env file into an existing BitBucket environment"
        );
    }

    #[test]
    fn test_workflow_type_debug() {
        // Test that WorkflowType can be debugged
        let env_to_env = WorkflowType::EnvToEnv;
        let file_to_new = WorkflowType::FileToNewEnv;
        let file_to_existing = WorkflowType::FileToExistingEnv;

        assert_eq!(format!("{:?}", env_to_env), "EnvToEnv");
        assert_eq!(format!("{:?}", file_to_new), "FileToNewEnv");
        assert_eq!(format!("{:?}", file_to_existing), "FileToExistingEnv");
    }

    #[test]
    fn test_args_workflow_detection() {
        let args_env_to_env = create_test_args_env_to_env();
        match &args_env_to_env.workflow {
            Some(WorkflowCommand::EnvToEnv) => {}
            _ => panic!("Expected EnvToEnv workflow"),
        }

        let args_file_to_new = create_test_args_file_to_new(Some("test.env".to_string()));
        match &args_file_to_new.workflow {
            Some(WorkflowCommand::FileToNew { file }) => {
                assert_eq!(file, &Some("test.env".to_string()));
            }
            _ => panic!("Expected FileToNew workflow"),
        }

        let args_file_to_existing = create_test_args_file_to_existing(
            Some("test.env".to_string()),
            "overwrite".to_string(),
        );
        match &args_file_to_existing.workflow {
            Some(WorkflowCommand::FileToExisting {
                file,
                conflict_strategy,
            }) => {
                assert_eq!(file, &Some("test.env".to_string()));
                assert_eq!(conflict_strategy, "overwrite");
            }
            _ => panic!("Expected FileToExisting workflow"),
        }
    }

    #[test]
    fn test_env_file_creation_for_testing() {
        let temp_file = create_test_env_file();
        let content = std::fs::read_to_string(temp_file.path()).unwrap();

        assert!(content.contains("VAR1=value1"));
        assert!(content.contains("VAR2=value2"));
        assert!(content.contains("SECRET_KEY=secret123"));
        assert!(content.contains("# Test environment file"));
    }

    // Note: Actual workflow execution tests would require mocking the BitBucket API
    // or integration test setup, which is beyond the scope of unit tests.
    // The workflows are tested through their individual components (parser, client, etc.)
}
