use crate::{
    auth::BitbucketCredentials,
    errors::{A<PERSON><PERSON><PERSON><PERSON>, AppError, <PERSON>pp<PERSON><PERSON><PERSON>},
    structs::{
        DeploymentEnvironment, EnvironmentValue, PaginatedResponse, Repository, VariableValue,
    },
};
use reqwest::{blocking::Client, Method};
use serde::Serialize;

/// BitBucket API client for managing repositories, environments, and variables
pub struct BitbucketClient {
    client: Client,
    credentials: BitbucketCredentials,
    base_url: String,
    verbose: bool,
}

#[derive(Clone)]
enum RequestData<T: Serialize + Clone> {
    Json(T),
    NoData,
}

impl BitbucketClient {
    /// Create a new BitBucket client
    pub fn new(credentials: BitbucketCredentials, verbose: bool) -> Self {
        Self {
            client: Client::new(),
            credentials,
            base_url: "https://api.bitbucket.org/2.0".to_string(),
            verbose,
        }
    }

    /// Load all repositories for a workspace with pagination
    pub fn load_repositories(&self, workspace: &str) -> AppResult<Vec<Repository>> {
        let mut all_repositories = Vec::new();
        let mut current_page = 1;
        const PER_PAGE: i64 = 100;

        loop {
            let page_result = self.load_repositories_page(workspace, current_page, PER_PAGE)?;

            all_repositories.extend(page_result.values);

            if page_result.next.is_none() {
                break;
            }

            current_page += 1;
        }

        if self.verbose {
            println!(
                "Loaded {} repositories across {} pages",
                all_repositories.len(),
                current_page
            );
        }

        Ok(all_repositories)
    }

    /// Load environments for a repository
    pub fn load_environments(
        &self,
        workspace: &str,
        repo_slug: &str,
    ) -> AppResult<Vec<EnvironmentValue>> {
        let url = format!(
            "{}/repositories/{}/{}/environments/",
            self.base_url, workspace, repo_slug
        );

        let response = self.make_request::<()>(Method::GET, &url, RequestData::NoData)?;
        let environments: PaginatedResponse<EnvironmentValue> = response.json().map_err(|e| {
            AppError::Api(ApiError::InvalidResponse(format!(
                "Failed to parse environments response from {}: {}",
                url, e
            )))
        })?;

        Ok(environments.values)
    }

    /// Load all variables for an environment with pagination
    pub fn load_environment_variables(
        &self,
        workspace: &str,
        repo_slug: &str,
        env_uuid: &str,
    ) -> AppResult<Vec<VariableValue>> {
        let mut all_variables = Vec::new();
        let mut current_page = 1;
        const PER_PAGE: i64 = 100;

        loop {
            let page_result =
                self.load_variables_page(workspace, repo_slug, env_uuid, current_page, PER_PAGE)?;

            all_variables.extend(page_result.values);

            if page_result.pagelen == 0 {
                break;
            }

            current_page += 1;
        }

        Ok(all_variables)
    }

    /// Create a new environment
    pub fn create_environment(
        &self,
        workspace: &str,
        repo_slug: &str,
        environment: &DeploymentEnvironment,
    ) -> AppResult<DeploymentEnvironment> {
        let url = format!(
            "{}/repositories/{}/{}/environments/",
            self.base_url, workspace, repo_slug
        );

        let response = self.make_request(Method::POST, &url, RequestData::Json(environment))?;
        let created_env: DeploymentEnvironment = response.json().map_err(|e| {
            AppError::Api(ApiError::InvalidResponse(format!(
                "Failed to parse create environment response from {}: {}",
                url, e
            )))
        })?;

        Ok(created_env)
    }

    /// Create a variable in an environment
    pub fn create_variable(
        &self,
        workspace: &str,
        repo_slug: &str,
        env_uuid: &str,
        variable: &VariableValue,
    ) -> AppResult<()> {
        let url = format!(
            "{}/repositories/{}/{}/deployments_config/environments/{}/variables",
            self.base_url, workspace, repo_slug, env_uuid
        );

        self.make_request(Method::POST, &url, RequestData::Json(variable))?;
        Ok(())
    }

    /// Update a variable in an environment
    pub fn update_variable(
        &self,
        workspace: &str,
        repo_slug: &str,
        env_uuid: &str,
        variable_uuid: &str,
        variable: &VariableValue,
    ) -> AppResult<()> {
        let url = format!(
            "{}/repositories/{}/{}/deployments_config/environments/{}/variables/{}",
            self.base_url, workspace, repo_slug, env_uuid, variable_uuid
        );

        self.make_request(Method::PUT, &url, RequestData::Json(variable))?;
        Ok(())
    }

    /// Check if a variable exists in an environment
    #[allow(dead_code)]
    pub fn variable_exists(
        &self,
        workspace: &str,
        repo_slug: &str,
        env_uuid: &str,
        variable_key: &str,
    ) -> AppResult<Option<VariableValue>> {
        let variables = self.load_environment_variables(workspace, repo_slug, env_uuid)?;
        Ok(variables.into_iter().find(|v| v.key == variable_key))
    }

    /// Load a single page of repositories
    fn load_repositories_page(
        &self,
        workspace: &str,
        page: i64,
        per_page: i64,
    ) -> AppResult<PaginatedResponse<Repository>> {
        let url = format!(
            "{}/repositories/{}?page={}&pagelen={}",
            self.base_url, workspace, page, per_page
        );

        let response = self.make_request::<()>(Method::GET, &url, RequestData::NoData)?;
        let repos: PaginatedResponse<Repository> = response.json().map_err(|e| {
            AppError::Api(ApiError::InvalidResponse(format!(
                "Failed to parse repositories response from {}: {}",
                url, e
            )))
        })?;

        Ok(repos)
    }

    /// Load a single page of variables
    fn load_variables_page(
        &self,
        workspace: &str,
        repo_slug: &str,
        env_uuid: &str,
        page: i64,
        per_page: i64,
    ) -> AppResult<PaginatedResponse<VariableValue>> {
        let url = format!(
            "{}/repositories/{}/{}/deployments_config/environments/{}/variables?page={}&pagelen={}",
            self.base_url, workspace, repo_slug, env_uuid, page, per_page
        );

        let response = self.make_request::<()>(Method::GET, &url, RequestData::NoData)?;
        let variables: PaginatedResponse<VariableValue> = response.json().map_err(|e| {
            AppError::Api(ApiError::InvalidResponse(format!(
                "Failed to parse variables response from {}: {}",
                url, e
            )))
        })?;

        Ok(variables)
    }

    /// Make an HTTP request to the BitBucket API
    fn make_request<T: Serialize + Clone>(
        &self,
        method: Method,
        url: &str,
        data: RequestData<T>,
    ) -> AppResult<reqwest::blocking::Response> {
        let request_builder = match data {
            RequestData::Json(json_data) => {
                if self.verbose {
                    println!("Calling: {}", url);
                    if let Ok(pretty_json) = serde_json::to_string_pretty(&json_data) {
                        println!("POST Data:\n{}", pretty_json);
                    }
                }

                let headers = self
                    .credentials
                    .create_json_headers()
                    .map_err(|e| AppError::Authentication(e.to_string()))?;

                let json_string = serde_json::to_string(&json_data).map_err(|e| {
                    AppError::Api(ApiError::InvalidResponse(format!(
                        "Failed to serialize request data for {}: {}",
                        url, e
                    )))
                })?;

                self.client
                    .request(method, url)
                    .headers(headers)
                    .body(json_string)
            }
            RequestData::NoData => {
                if self.verbose {
                    println!("Calling: {}", url);
                }

                let headers = self
                    .credentials
                    .create_get_headers()
                    .map_err(|e| AppError::Authentication(e.to_string()))?;

                self.client.request(method, url).headers(headers)
            }
        };

        let response = request_builder.send()?;

        if response.status().is_success() {
            Ok(response)
        } else {
            let status = response.status();
            let error_text = response
                .text()
                .unwrap_or_else(|_| "Unknown error".to_string());

            let api_error = match status.as_u16() {
                401 | 403 => ApiError::Unauthorized(format!("URL: {} - {}", url, error_text)),
                404 => ApiError::NotFound(format!("URL: {} - {}", url, error_text)),
                429 => ApiError::RateLimited(format!("URL: {} - {}", url, error_text)),
                500..=599 => ApiError::ServerError(format!("URL: {} - {}", url, error_text)),
                _ => ApiError::RequestFailed(format!(
                    "URL: {} - HTTP {}: {}",
                    url, status, error_text
                )),
            };

            Err(AppError::Api(api_error))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::auth::BitbucketCredentials;
    use serde_json::json;

    #[test]
    fn test_client_creation() {
        let credentials =
            BitbucketCredentials::new("<EMAIL>".to_string(), "test_token".to_string());
        let client = BitbucketClient::new(credentials, false);

        assert_eq!(client.base_url, "https://api.bitbucket.org/2.0");
        assert!(!client.verbose);
    }

    #[test]
    fn test_client_creation_with_verbose() {
        let credentials =
            BitbucketCredentials::new("<EMAIL>".to_string(), "test_token".to_string());
        let client = BitbucketClient::new(credentials, true);

        assert!(client.verbose);
    }

    #[test]
    fn test_request_data_enum() {
        let test_data = json!({"key": "value"});
        let json_data = RequestData::Json(test_data.clone());
        let no_data: RequestData<serde_json::Value> = RequestData::NoData;

        match json_data {
            RequestData::Json(data) => assert_eq!(data, test_data),
            _ => panic!("Expected Json variant"),
        }

        match no_data {
            RequestData::NoData => {}
            _ => panic!("Expected NoData variant"),
        }
    }
}
