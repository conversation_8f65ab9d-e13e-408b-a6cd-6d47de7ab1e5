mod args;
mod auth;
mod bitbucket_client;
mod conflict_resolver;
mod env_file_parser;
mod errors;
mod structs;
mod variable_operations;
mod workflows;

use crate::{
    args::{Args, WorkflowCommand},
    auth::BitbucketCredentials,
    bitbucket_client::BitbucketClient,
    errors::AppResult,
    workflows::{WorkflowFactory, WorkflowType},
};
use clap::Parser;
use colored::Colorize;
use inquire::Select;

fn main() -> AppResult<()> {
    let args = Args::parse();

    // Get credentials with proper error handling
    let username = args
        .get_username()
        .map_err(|e| crate::errors::AppError::Authentication(e))?;
    let api_token = args
        .get_api_token()
        .map_err(|e| crate::errors::AppError::Authentication(e))?;
    let credentials = BitbucketCredentials::new(username, api_token);

    // Create BitBucket client
    let client = BitbucketClient::new(credentials, args.verbose);

    // Determine workflow to execute
    let workflow_type = match &args.workflow {
        Some(WorkflowCommand::EnvToEnv) => WorkflowType::EnvToEnv,
        Some(WorkflowCommand::FileToNew { .. }) => WorkflowType::FileToNewEnv,
        Some(WorkflowCommand::FileToExisting { .. }) => WorkflowType::FileToExistingEnv,
        None => {
            // Interactive workflow selection for backward compatibility
            select_workflow_interactively()?
        }
    };

    // Create and execute workflow
    let workflow = WorkflowFactory::create_workflow(workflow_type);
    workflow.execute(&args, &client)?;

    Ok(())
}

/// Interactive workflow selection for backward compatibility
fn select_workflow_interactively() -> AppResult<WorkflowType> {
    println!(
        "{} Welcome to BitBucket Environment Variable Manager",
        "🚀".green()
    );
    println!();

    let workflows = WorkflowFactory::list_workflows();
    let workflow_options: Vec<String> = workflows
        .iter()
        .map(|(_, name, description)| format!("{}: {}", name, description))
        .collect();

    let selected = Select::new("Choose a workflow:", workflow_options.clone())
        .prompt()
        .map_err(|_| {
            crate::errors::AppError::Workflow(crate::errors::WorkflowError::UserCancelled)
        })?;

    // Extract workflow type from selection
    let workflow_index = workflow_options
        .iter()
        .position(|x| x == &selected)
        .unwrap();
    Ok(workflows[workflow_index].0.clone())
}
