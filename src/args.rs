use clap::{Parser, Subcommand};
use std::env;

#[derive(Parse<PERSON>, Debug)]
#[command(name = "bb-copy-env")]
#[command(about = "BitBucket environment variable management tool")]
#[command(version)]
pub struct Args {
    /// Atlassian account email address (username for Basic Auth). Can also be set via BITBUCKET_USERNAME environment variable.
    #[arg(short, long, env = "BITBUCKET_USERNAME")]
    pub username: Option<String>,

    /// BitBucket API token (password for Basic Auth). Can also be set via BITBUCKET_API_TOKEN environment variable.
    #[arg(short = 't', long = "api-token", env = "BITBUCKET_API_TOKEN")]
    pub api_token: Option<String>,

    /// A BitBucket Workspace.
    #[arg(short, long, default_value_t = String::from("technofarm"))]
    pub workspace: String,

    /// Show verbose output
    #[arg(short, long, default_value_t = false)]
    pub verbose: bool,

    /// Workflow to execute
    #[command(subcommand)]
    pub workflow: Option<WorkflowCommand>,
}

#[derive(Subcommand, Debug)]
pub enum WorkflowCommand {
    /// Copy variables from one BitBucket environment to another
    #[command(name = "env-to-env")]
    EnvToEnv,

    /// Create a new BitBucket environment from a .env file
    #[command(name = "file-to-new")]
    FileToNew {
        /// Path to the .env file
        #[arg(short, long)]
        file: Option<String>,
    },

    /// Append variables from a .env file to an existing BitBucket environment
    #[command(name = "file-to-existing")]
    FileToExisting {
        /// Path to the .env file
        #[arg(short, long)]
        file: Option<String>,

        /// Conflict resolution strategy: overwrite, skip, prompt, merge
        #[arg(short, long, default_value = "prompt")]
        conflict_strategy: String,
    },
}

impl Args {
    /// Get username from args or environment variable, with validation
    pub fn get_username(&self) -> Result<String, String> {
        self.username
            .clone()
            .or_else(|| env::var("BITBUCKET_USERNAME").ok())
            .ok_or_else(|| "Username is required. Provide via --username argument or BITBUCKET_USERNAME environment variable.".to_string())
    }

    /// Get API token from args or environment variable, with validation
    pub fn get_api_token(&self) -> Result<String, String> {
        self.api_token
            .clone()
            .or_else(|| env::var("BITBUCKET_API_TOKEN").ok())
            .ok_or_else(|| "API token is required. Provide via --api-token argument or BITBUCKET_API_TOKEN environment variable.".to_string())
    }

    /// Get the file path for file-based workflows
    #[allow(dead_code)]
    pub fn get_file_path(&self) -> Option<String> {
        match &self.workflow {
            Some(WorkflowCommand::FileToNew { file }) => file.clone(),
            Some(WorkflowCommand::FileToExisting { file, .. }) => file.clone(),
            _ => None,
        }
    }

    /// Get the conflict resolution strategy for file-to-existing workflow
    #[allow(dead_code)]
    pub fn get_conflict_strategy(&self) -> Option<String> {
        match &self.workflow {
            Some(WorkflowCommand::FileToExisting {
                conflict_strategy, ..
            }) => Some(conflict_strategy.clone()),
            _ => None,
        }
    }
}
