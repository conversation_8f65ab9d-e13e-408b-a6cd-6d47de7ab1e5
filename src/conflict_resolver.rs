use crate::{
    errors::{A<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ConflictError, WorkflowError},
    structs::VariableValue,
    variable_operations::{VariableConflict, VariableOperations},
};
use colored::Colorize;
use inquire::{Confirm, Select};

/// Strategies for resolving variable conflicts
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum ConflictStrategy {
    /// Overwrite existing variables with new values
    Overwrite,
    /// Skip conflicting variables, keep existing values
    Skip,
    /// Prompt user for each conflict
    Prompt,
    /// Merge values (for specific cases)
    Merge,
}

impl ConflictStrategy {
    /// Parse strategy from string
    pub fn from_str(s: &str) -> AppResult<Self> {
        match s.to_lowercase().as_str() {
            "overwrite" | "replace" => Ok(ConflictStrategy::Overwrite),
            "skip" | "keep" => Ok(ConflictStrategy::Skip),
            "prompt" | "ask" => Ok(ConflictStrategy::Prompt),
            "merge" => Ok(ConflictStrategy::Merge),
            _ => Err(AppError::Conflict(ConflictError::InvalidStrategy(format!(
                "Unknown strategy: {}. Valid options: overwrite, skip, prompt, merge",
                s
            )))),
        }
    }

    /// Get all available strategies as strings
    #[allow(dead_code)]
    pub fn all_strategies() -> Vec<&'static str> {
        vec!["overwrite", "skip", "prompt", "merge"]
    }
}

/// Conflict resolver for handling variable conflicts
pub struct ConflictResolver {
    strategy: ConflictStrategy,
    interactive: bool,
}

impl ConflictResolver {
    /// Create a new conflict resolver with the specified strategy
    pub fn new(strategy: ConflictStrategy, interactive: bool) -> Self {
        Self {
            strategy,
            interactive,
        }
    }

    /// Resolve conflicts between existing and new variables
    pub fn resolve_conflicts(
        &self,
        existing_variables: &[VariableValue],
        new_variables: &[VariableValue],
    ) -> AppResult<ConflictResolution> {
        let differences = VariableOperations::find_differences(existing_variables, new_variables);

        if differences.conflicts.is_empty() {
            return Ok(ConflictResolution {
                to_add: differences.to_add,
                to_update: Vec::new(),
                skipped: Vec::new(),
                resolution_summary: "No conflicts found".to_string(),
            });
        }

        // Display conflicts summary
        if self.interactive {
            self.display_conflicts_summary(&differences.conflicts);
        }

        let to_add = differences.to_add;
        let mut to_update = Vec::new();
        let mut skipped = Vec::new();
        let mut resolution_actions = Vec::new();

        for conflict in differences.conflicts {
            let resolution = self.resolve_single_conflict(&conflict)?;

            match resolution {
                SingleConflictResolution::Overwrite => {
                    // Find the existing variable to get its UUID for update
                    if let Some(existing_var) =
                        existing_variables.iter().find(|v| v.key == conflict.key)
                    {
                        let mut updated_var = existing_var.clone();
                        updated_var.value = conflict.new_value.clone();
                        updated_var.secured = conflict.new_secured;
                        to_update.push((existing_var.uuid.clone(), updated_var));
                        resolution_actions.push(format!("Overwrite {}", conflict.key));
                    }
                }
                SingleConflictResolution::Skip => {
                    skipped.push(conflict.key.clone());
                    resolution_actions.push(format!("Skip {}", conflict.key));
                }
                SingleConflictResolution::Merge => {
                    // For now, merge means concatenate with a separator
                    if let Some(existing_var) =
                        existing_variables.iter().find(|v| v.key == conflict.key)
                    {
                        let mut updated_var = existing_var.clone();
                        updated_var.value =
                            format!("{};{}", conflict.existing_value, conflict.new_value);
                        to_update.push((existing_var.uuid.clone(), updated_var));
                        resolution_actions.push(format!("Merge {}", conflict.key));
                    }
                }
            }
        }

        let resolution_summary = if resolution_actions.is_empty() {
            "No conflicts resolved".to_string()
        } else {
            format!(
                "Resolved {} conflicts: {}",
                resolution_actions.len(),
                resolution_actions.join(", ")
            )
        };

        Ok(ConflictResolution {
            to_add,
            to_update,
            skipped,
            resolution_summary,
        })
    }

    /// Resolve a single conflict based on the strategy
    fn resolve_single_conflict(
        &self,
        conflict: &VariableConflict,
    ) -> AppResult<SingleConflictResolution> {
        match self.strategy {
            ConflictStrategy::Overwrite => Ok(SingleConflictResolution::Overwrite),
            ConflictStrategy::Skip => Ok(SingleConflictResolution::Skip),
            ConflictStrategy::Merge => Ok(SingleConflictResolution::Merge),
            ConflictStrategy::Prompt => {
                if !self.interactive {
                    return Err(AppError::Conflict(ConflictError::UserInputRequired(
                        "Prompt strategy requires interactive mode".to_string(),
                    )));
                }
                self.prompt_for_resolution(conflict)
            }
        }
    }

    /// Prompt user for conflict resolution
    fn prompt_for_resolution(
        &self,
        conflict: &VariableConflict,
    ) -> AppResult<SingleConflictResolution> {
        println!("\n{} Variable conflict detected:", "⚠️".yellow());
        println!("  Variable: {}", conflict.key.cyan());

        if conflict.involves_secured() {
            println!("  {} This variable involves secured values", "🔒".red());
        }

        println!("  Existing value: {}", conflict.existing_value.green());
        println!("  New value:      {}", conflict.new_value.blue());

        let options = vec![
            "Overwrite (use new value)",
            "Skip (keep existing value)",
            "Merge (combine both values)",
        ];

        let selection = Select::new(
            &format!("How should '{}' be resolved?", conflict.key),
            options,
        )
        .prompt()
        .map_err(|_e| AppError::Workflow(WorkflowError::UserCancelled))?;

        match selection {
            "Overwrite (use new value)" => Ok(SingleConflictResolution::Overwrite),
            "Skip (keep existing value)" => Ok(SingleConflictResolution::Skip),
            "Merge (combine both values)" => {
                // Ask for confirmation on merge since it might not always make sense
                let confirm = Confirm::new("Are you sure you want to merge these values? This will concatenate them with a semicolon.")
                    .with_default(false)
                    .prompt()
                    .map_err(|_| AppError::Workflow(WorkflowError::UserCancelled))?;

                if confirm {
                    Ok(SingleConflictResolution::Merge)
                } else {
                    // Recursively ask again
                    self.prompt_for_resolution(conflict)
                }
            }
            _ => Err(AppError::Conflict(ConflictError::InvalidStrategy(
                "Invalid selection".to_string(),
            ))),
        }
    }

    /// Display conflicts summary
    fn display_conflicts_summary(&self, conflicts: &[VariableConflict]) {
        println!(
            "\n{} Found {} variable conflicts:",
            "⚠️".yellow(),
            conflicts.len()
        );

        for (i, conflict) in conflicts.iter().enumerate() {
            println!("  {}. {}", i + 1, conflict.display_summary());
        }

        println!(
            "\nConflict resolution strategy: {}",
            match self.strategy {
                ConflictStrategy::Overwrite => "Overwrite existing values".red(),
                ConflictStrategy::Skip => "Skip conflicting variables".yellow(),
                ConflictStrategy::Prompt => "Prompt for each conflict".blue(),
                ConflictStrategy::Merge => "Merge values".green(),
            }
        );
        println!();
    }
}

/// Result of resolving a single conflict
#[derive(Debug, Clone)]
enum SingleConflictResolution {
    Overwrite,
    Skip,
    Merge,
}

/// Result of conflict resolution process
#[derive(Debug, Clone)]
pub struct ConflictResolution {
    /// Variables to add (no conflicts)
    pub to_add: Vec<VariableValue>,
    /// Variables to update (conflicts resolved with overwrite/merge)
    pub to_update: Vec<(String, VariableValue)>, // (uuid, variable)
    /// Variables that were skipped due to conflicts
    pub skipped: Vec<String>,
    /// Summary of resolution actions taken
    pub resolution_summary: String,
}

impl ConflictResolution {
    /// Check if any variables will be modified
    pub fn has_changes(&self) -> bool {
        !self.to_add.is_empty() || !self.to_update.is_empty()
    }

    /// Get total number of variables that will be processed
    pub fn total_changes(&self) -> usize {
        self.to_add.len() + self.to_update.len()
    }

    /// Display resolution summary
    pub fn display_summary(&self) {
        if !self.has_changes() {
            println!("{} No changes to apply", "ℹ️".blue());
            return;
        }

        println!("{} Conflict resolution summary:", "📋".green());

        if !self.to_add.is_empty() {
            println!("  {} Variables to add: {}", "➕".green(), self.to_add.len());
            for var in &self.to_add {
                println!("    + {}", var.key.green());
            }
        }

        if !self.to_update.is_empty() {
            println!(
                "  {} Variables to update: {}",
                "🔄".yellow(),
                self.to_update.len()
            );
            for (_, var) in &self.to_update {
                println!("    ~ {}", var.key.yellow());
            }
        }

        if !self.skipped.is_empty() {
            println!("  {} Variables skipped: {}", "⏭️".red(), self.skipped.len());
            for key in &self.skipped {
                println!("    - {}", key.red());
            }
        }

        println!("\n{}", self.resolution_summary);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_variable(key: &str, value: &str, uuid: &str) -> VariableValue {
        VariableValue {
            type_field: "deployment_variable".to_string(),
            uuid: uuid.to_string(),
            key: key.to_string(),
            value: value.to_string(),
            secured: false,
        }
    }

    #[test]
    fn test_conflict_strategy_from_str() {
        assert_eq!(
            ConflictStrategy::from_str("overwrite").unwrap(),
            ConflictStrategy::Overwrite
        );
        assert_eq!(
            ConflictStrategy::from_str("skip").unwrap(),
            ConflictStrategy::Skip
        );
        assert_eq!(
            ConflictStrategy::from_str("prompt").unwrap(),
            ConflictStrategy::Prompt
        );
        assert!(ConflictStrategy::from_str("invalid").is_err());
    }

    #[test]
    fn test_resolve_conflicts_no_conflicts() {
        let resolver = ConflictResolver::new(ConflictStrategy::Overwrite, false);
        let existing = vec![create_test_variable("VAR1", "value1", "uuid1")];
        let new = vec![create_test_variable("VAR2", "value2", "")];

        let resolution = resolver.resolve_conflicts(&existing, &new).unwrap();

        assert_eq!(resolution.to_add.len(), 1);
        assert_eq!(resolution.to_update.len(), 0);
        assert_eq!(resolution.skipped.len(), 0);
    }

    #[test]
    fn test_resolve_conflicts_overwrite_strategy() {
        let resolver = ConflictResolver::new(ConflictStrategy::Overwrite, false);
        let existing = vec![create_test_variable("VAR1", "old_value", "uuid1")];
        let new = vec![create_test_variable("VAR1", "new_value", "")];

        let resolution = resolver.resolve_conflicts(&existing, &new).unwrap();

        assert_eq!(resolution.to_add.len(), 0);
        assert_eq!(resolution.to_update.len(), 1);
        assert_eq!(resolution.skipped.len(), 0);
        assert_eq!(resolution.to_update[0].1.value, "new_value");
    }

    #[test]
    fn test_resolve_conflicts_skip_strategy() {
        let resolver = ConflictResolver::new(ConflictStrategy::Skip, false);
        let existing = vec![create_test_variable("VAR1", "old_value", "uuid1")];
        let new = vec![create_test_variable("VAR1", "new_value", "")];

        let resolution = resolver.resolve_conflicts(&existing, &new).unwrap();

        assert_eq!(resolution.to_add.len(), 0);
        assert_eq!(resolution.to_update.len(), 0);
        assert_eq!(resolution.skipped.len(), 1);
        assert_eq!(resolution.skipped[0], "VAR1");
    }
}
