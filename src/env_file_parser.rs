use crate::{
    errors::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oError, ValidationError},
    structs::VariableValue,
};
use std::{
    collections::HashMap,
    fs::File,
    io::{BufRead, BufReader},
    path::Path,
};

/// Environment file parser for reading .env files and converting them to VariableValue structs
pub struct EnvFileParser {
    /// Whether to allow empty values
    pub allow_empty_values: bool,
    /// Whether to trim whitespace from values
    pub trim_values: bool,
    /// Whether to skip invalid lines instead of failing
    pub skip_invalid_lines: bool,
}

impl Default for EnvFileParser {
    fn default() -> Self {
        Self {
            allow_empty_values: true,
            trim_values: true,
            skip_invalid_lines: false,
        }
    }
}

impl EnvFileParser {
    /// Create a new parser with default settings
    pub fn new() -> Self {
        Self::default()
    }

    /// Create a new parser with custom settings
    #[allow(dead_code)]
    pub fn with_options(
        allow_empty_values: bool,
        trim_values: bool,
        skip_invalid_lines: bool,
    ) -> Self {
        Self {
            allow_empty_values,
            trim_values,
            skip_invalid_lines,
        }
    }

    /// Parse an environment file and return a vector of VariableValue structs
    pub fn parse_file<P: AsRef<Path>>(&self, file_path: P) -> AppResult<Vec<VariableValue>> {
        let path = file_path.as_ref();

        if !path.exists() {
            return Err(AppError::FileIo(FileIoError::FileNotFound(
                path.display().to_string(),
            )));
        }

        let file = File::open(path).map_err(|e| {
            if e.kind() == std::io::ErrorKind::PermissionDenied {
                AppError::FileIo(FileIoError::PermissionDenied(path.display().to_string()))
            } else {
                AppError::FileIo(FileIoError::ParseError(e.to_string()))
            }
        })?;

        let reader = BufReader::new(file);
        let mut variables = Vec::new();
        let mut line_number = 0;

        for line_result in reader.lines() {
            line_number += 1;
            let line = line_result.map_err(|e| {
                AppError::FileIo(FileIoError::ParseError(format!(
                    "Error reading line {}: {}",
                    line_number, e
                )))
            })?;

            match self.parse_line(&line, line_number) {
                Ok(Some(variable)) => variables.push(variable),
                Ok(None) => continue, // Empty line or comment
                Err(e) => {
                    if self.skip_invalid_lines {
                        eprintln!("Warning: Skipping invalid line {}: {}", line_number, e);
                        continue;
                    } else {
                        return Err(e);
                    }
                }
            }
        }

        Ok(variables)
    }

    /// Parse environment variables from a string
    #[allow(dead_code)]
    pub fn parse_string(&self, content: &str) -> AppResult<Vec<VariableValue>> {
        let mut variables = Vec::new();
        let mut line_number = 0;

        for line in content.lines() {
            line_number += 1;
            match self.parse_line(line, line_number) {
                Ok(Some(variable)) => variables.push(variable),
                Ok(None) => continue,
                Err(e) => {
                    if self.skip_invalid_lines {
                        eprintln!("Warning: Skipping invalid line {}: {}", line_number, e);
                        continue;
                    } else {
                        return Err(e);
                    }
                }
            }
        }

        Ok(variables)
    }

    /// Convert VariableValue structs to a HashMap for easier manipulation
    #[allow(dead_code)]
    pub fn to_hashmap(variables: &[VariableValue]) -> HashMap<String, String> {
        variables
            .iter()
            .map(|var| (var.key.clone(), var.value.clone()))
            .collect()
    }

    /// Convert HashMap back to VariableValue structs
    #[allow(dead_code)]
    pub fn from_hashmap(map: &HashMap<String, String>) -> Vec<VariableValue> {
        map.iter()
            .map(|(key, value)| VariableValue {
                type_field: "deployment_variable".to_string(),
                uuid: String::new(), // Will be set by BitBucket API
                key: key.clone(),
                value: value.clone(),
                secured: Self::is_sensitive_variable(key),
            })
            .collect()
    }

    /// Write variables to an environment file
    #[allow(dead_code)]
    pub fn write_file<P: AsRef<Path>>(
        &self,
        file_path: P,
        variables: &[VariableValue],
    ) -> AppResult<()> {
        use std::io::Write;

        let path = file_path.as_ref();
        let mut file = File::create(path).map_err(|e| {
            AppError::FileIo(FileIoError::WriteError(format!(
                "Failed to create file {}: {}",
                path.display(),
                e
            )))
        })?;

        for variable in variables {
            let line = if variable.secured {
                format!("# SECURED: {}=<hidden>\n", variable.key)
            } else {
                format!("{}={}\n", variable.key, variable.value)
            };

            file.write_all(line.as_bytes()).map_err(|e| {
                AppError::FileIo(FileIoError::WriteError(format!(
                    "Failed to write to file {}: {}",
                    path.display(),
                    e
                )))
            })?;
        }

        Ok(())
    }

    /// Parse a single line from an environment file
    fn parse_line(&self, line: &str, line_number: usize) -> AppResult<Option<VariableValue>> {
        let line = line.trim();

        // Skip empty lines and comments
        if line.is_empty() || line.starts_with('#') {
            return Ok(None);
        }

        // Find the first '=' character
        let eq_pos = line.find('=').ok_or_else(|| {
            AppError::FileIo(FileIoError::ParseError(format!(
                "Line {}: Missing '=' separator in line: {}",
                line_number, line
            )))
        })?;

        let key = line[..eq_pos].trim();
        let value = line[eq_pos + 1..].trim();

        // Validate key
        if key.is_empty() {
            return Err(AppError::Validation(ValidationError::InvalidVariableName(
                format!("Line {}: Empty variable name", line_number),
            )));
        }

        if !Self::is_valid_variable_name(key) {
            return Err(AppError::Validation(ValidationError::InvalidVariableName(
                format!(
                    "Line {}: Invalid variable name '{}'. Variable names must contain only letters, numbers, and underscores, and cannot start with a number.",
                    line_number, key
                ),
            )));
        }

        // Handle value
        let final_value = if self.trim_values {
            value.to_string()
        } else {
            line[eq_pos + 1..].to_string()
        };

        if !self.allow_empty_values && final_value.is_empty() {
            return Err(AppError::Validation(ValidationError::InvalidVariableValue(
                format!("Line {}: Empty value for variable '{}'", line_number, key),
            )));
        }

        // Remove surrounding quotes if present
        let final_value = Self::remove_quotes(&final_value);

        Ok(Some(VariableValue {
            type_field: "deployment_variable".to_string(),
            uuid: String::new(), // Will be set by BitBucket API
            key: key.to_string(),
            value: final_value,
            secured: Self::is_sensitive_variable(key),
        }))
    }

    /// Check if a variable name is valid
    fn is_valid_variable_name(name: &str) -> bool {
        if name.is_empty() {
            return false;
        }

        // Must start with letter or underscore
        let first_char = match name.chars().next() {
            Some(c) => c,
            None => return false, // Empty string already handled above
        };
        if !first_char.is_ascii_alphabetic() && first_char != '_' {
            return false;
        }

        // Rest must be alphanumeric or underscore
        name.chars().all(|c| c.is_ascii_alphanumeric() || c == '_')
    }

    /// Check if a variable is likely to contain sensitive information
    pub fn is_sensitive_variable(key: &str) -> bool {
        let key_lower = key.to_lowercase();
        let sensitive_patterns = [
            "password",
            "passwd",
            "pwd",
            "secret",
            "key",
            "token",
            "auth",
            "api_key",
            "private",
            "credential",
            "cert",
            "ssl",
            "tls",
            "oauth",
            "jwt",
            "session",
        ];

        sensitive_patterns
            .iter()
            .any(|pattern| key_lower.contains(pattern))
    }

    /// Remove surrounding quotes from a value
    fn remove_quotes(value: &str) -> String {
        let trimmed = value.trim();
        if (trimmed.starts_with('"') && trimmed.ends_with('"'))
            || (trimmed.starts_with('\'') && trimmed.ends_with('\''))
        {
            trimmed[1..trimmed.len() - 1].to_string()
        } else {
            trimmed.to_string()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_valid_line() {
        let parser = EnvFileParser::new();
        let result = parser.parse_line("KEY=value", 1).unwrap();
        assert!(result.is_some());
        let var = result.unwrap();
        assert_eq!(var.key, "KEY");
        assert_eq!(var.value, "value");
    }

    #[test]
    fn test_parse_empty_line() {
        let parser = EnvFileParser::new();
        let result = parser.parse_line("", 1).unwrap();
        assert!(result.is_none());
    }

    #[test]
    fn test_parse_comment_line() {
        let parser = EnvFileParser::new();
        let result = parser.parse_line("# This is a comment", 1).unwrap();
        assert!(result.is_none());
    }

    #[test]
    fn test_sensitive_variable_detection() {
        assert!(EnvFileParser::is_sensitive_variable("API_KEY"));
        assert!(EnvFileParser::is_sensitive_variable("password"));
        assert!(EnvFileParser::is_sensitive_variable("SECRET_TOKEN"));
        assert!(!EnvFileParser::is_sensitive_variable("DATABASE_URL"));
        assert!(!EnvFileParser::is_sensitive_variable("PORT"));
    }

    #[test]
    fn test_valid_variable_names() {
        assert!(EnvFileParser::is_valid_variable_name("VALID_NAME"));
        assert!(EnvFileParser::is_valid_variable_name("_PRIVATE"));
        assert!(EnvFileParser::is_valid_variable_name("API_KEY_123"));
        assert!(!EnvFileParser::is_valid_variable_name("123_INVALID"));
        assert!(!EnvFileParser::is_valid_variable_name("INVALID-NAME"));
        assert!(!EnvFileParser::is_valid_variable_name(""));
    }

    #[test]
    fn test_remove_quotes() {
        assert_eq!(EnvFileParser::remove_quotes("\"quoted\""), "quoted");
        assert_eq!(EnvFileParser::remove_quotes("'single'"), "single");
        assert_eq!(EnvFileParser::remove_quotes("unquoted"), "unquoted");
        assert_eq!(EnvFileParser::remove_quotes("\"partial"), "\"partial");
    }

    #[test]
    fn test_parse_line_with_equals_in_value() {
        let parser = EnvFileParser::new();
        let result = parser.parse_line("KEY=value=with=equals", 1).unwrap();
        assert!(result.is_some());
        let var = result.unwrap();
        assert_eq!(var.key, "KEY");
        assert_eq!(var.value, "value=with=equals");
    }

    #[test]
    fn test_parse_line_with_spaces() {
        let parser = EnvFileParser::new();
        let result = parser.parse_line("  KEY  =  value  ", 1).unwrap();
        assert!(result.is_some());
        let var = result.unwrap();
        assert_eq!(var.key, "KEY");
        assert_eq!(var.value, "value");
    }

    #[test]
    fn test_parse_line_invalid_format() {
        let parser = EnvFileParser::new();
        let result = parser.parse_line("INVALID_LINE_WITHOUT_EQUALS", 1);
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_line_empty_key() {
        let parser = EnvFileParser::new();
        let result = parser.parse_line("=value", 1);
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_line_empty_value() {
        let parser = EnvFileParser::new();
        let result = parser.parse_line("KEY=", 1).unwrap();
        assert!(result.is_some());
        let var = result.unwrap();
        assert_eq!(var.key, "KEY");
        assert_eq!(var.value, "");
    }

    #[test]
    fn test_parse_file_nonexistent() {
        let parser = EnvFileParser::new();
        let result = parser.parse_file("nonexistent.env");
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_file_with_tempfile() {
        use std::io::Write;
        use tempfile::NamedTempFile;

        let parser = EnvFileParser::new();
        let mut temp_file = NamedTempFile::new().unwrap();
        writeln!(temp_file, "# Comment line").unwrap();
        writeln!(temp_file, "").unwrap();
        writeln!(temp_file, "VAR1=value1").unwrap();
        writeln!(temp_file, "VAR2=\"quoted value\"").unwrap();
        writeln!(temp_file, "SECRET_KEY=secret123").unwrap();
        temp_file.flush().unwrap();

        let result = parser
            .parse_file(temp_file.path().to_str().unwrap())
            .unwrap();

        assert_eq!(result.len(), 3);
        assert_eq!(result[0].key, "VAR1");
        assert_eq!(result[0].value, "value1");
        assert!(!result[0].secured);

        assert_eq!(result[1].key, "VAR2");
        assert_eq!(result[1].value, "quoted value");

        assert_eq!(result[2].key, "SECRET_KEY");
        assert_eq!(result[2].value, "secret123");
        assert!(result[2].secured); // Should be marked as sensitive
    }

    #[test]
    fn test_sensitive_variable_patterns() {
        // Test various sensitive patterns
        assert!(EnvFileParser::is_sensitive_variable("API_KEY"));
        assert!(EnvFileParser::is_sensitive_variable("SECRET_TOKEN"));
        assert!(EnvFileParser::is_sensitive_variable("PASSWORD"));
        assert!(EnvFileParser::is_sensitive_variable("PRIVATE_KEY"));
        assert!(EnvFileParser::is_sensitive_variable("AUTH_TOKEN"));
        assert!(EnvFileParser::is_sensitive_variable("JWT_SECRET"));
        assert!(EnvFileParser::is_sensitive_variable("ENCRYPTION_KEY"));

        // Test non-sensitive variables
        assert!(!EnvFileParser::is_sensitive_variable("DATABASE_URL"));
        assert!(!EnvFileParser::is_sensitive_variable("PORT"));
        assert!(!EnvFileParser::is_sensitive_variable("NODE_ENV"));
        assert!(!EnvFileParser::is_sensitive_variable("LOG_LEVEL"));
    }

    #[test]
    fn test_variable_name_validation_edge_cases() {
        // Valid names
        assert!(EnvFileParser::is_valid_variable_name("A"));
        assert!(EnvFileParser::is_valid_variable_name("_"));
        assert!(EnvFileParser::is_valid_variable_name("_123"));
        assert!(EnvFileParser::is_valid_variable_name("VAR_123_ABC"));

        // Invalid names
        assert!(!EnvFileParser::is_valid_variable_name(""));
        assert!(!EnvFileParser::is_valid_variable_name("123"));
        assert!(!EnvFileParser::is_valid_variable_name("123_VAR"));
        assert!(!EnvFileParser::is_valid_variable_name("VAR-NAME"));
        assert!(!EnvFileParser::is_valid_variable_name("VAR.NAME"));
        assert!(!EnvFileParser::is_valid_variable_name("VAR NAME"));
    }
}
