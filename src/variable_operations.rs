use crate::{
    bitbucket_client::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    errors::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ValidationError},
    structs::VariableValue,
};
use colored::Colorize;
use indicatif::{ProgressBar, ProgressStyle};
use std::collections::HashMap;

/// Utility functions for managing environment variables
pub struct VariableOperations;

impl VariableOperations {
    /// Filter out variables with empty values and provide user feedback
    /// Returns (valid_variables, skipped_variable_names)
    pub fn filter_empty_values(variables: Vec<VariableValue>) -> (Vec<VariableValue>, Vec<String>) {
        let mut valid_variables = Vec::new();
        let mut skipped_names = Vec::new();

        for variable in variables {
            if variable.value.trim().is_empty() {
                skipped_names.push(variable.key);
            } else {
                valid_variables.push(variable);
            }
        }

        // Display feedback to user if any variables were skipped
        if !skipped_names.is_empty() {
            println!(
                "{} Skipped {} variables with empty values: {}",
                "⚠️".yellow(),
                skipped_names.len(),
                skipped_names.join(", ").yellow()
            );
        }

        (valid_variables, skipped_names)
    }

    /// Validate a collection of variables
    pub fn validate_variables(variables: &[VariableValue]) -> AppResult<()> {
        let mut seen_keys = HashMap::new();

        for (index, variable) in variables.iter().enumerate() {
            // Check for empty key
            if variable.key.is_empty() {
                return Err(AppError::Validation(ValidationError::InvalidVariableName(
                    format!("Variable at index {} has empty key", index),
                )));
            }

            // Check for empty value - this is now handled by filter_empty_values
            // but we still validate here for completeness
            if variable.value.trim().is_empty() {
                return Err(AppError::Validation(ValidationError::InvalidVariableValue(
                    format!("Variable '{}' at index {} has empty value. All variables must have non-empty values before submission to BitBucket.", variable.key, index),
                )));
            }

            // Check for duplicate keys
            if let Some(first_index) = seen_keys.get(&variable.key) {
                return Err(AppError::Validation(ValidationError::InvalidVariableName(
                    format!(
                        "Duplicate variable key '{}' found at indices {} and {}",
                        variable.key, first_index, index
                    ),
                )));
            }
            seen_keys.insert(variable.key.clone(), index);

            // Validate key format
            if !Self::is_valid_variable_name(&variable.key) {
                return Err(AppError::Validation(ValidationError::InvalidVariableName(
                    format!(
                        "Invalid variable name '{}' at index {}. Variable names must contain only letters, numbers, and underscores, and cannot start with a number.",
                        variable.key, index
                    ),
                )));
            }
        }

        Ok(())
    }

    /// Filter out secured variables from a collection
    pub fn filter_secured_variables(
        variables: &[VariableValue],
    ) -> (Vec<VariableValue>, Vec<VariableValue>) {
        let mut secured = Vec::new();
        let mut unsecured = Vec::new();

        for variable in variables {
            if variable.secured {
                secured.push(variable.clone());
            } else {
                unsecured.push(variable.clone());
            }
        }

        (secured, unsecured)
    }

    /// Display secured variables warning
    pub fn display_secured_variables_warning(secured_variables: &[VariableValue]) {
        if !secured_variables.is_empty() {
            let warning =
                "The following variables will be skipped because they are secured:".yellow();
            println!("{}", warning);

            for variable in secured_variables {
                println!("  🔒 {}", variable.key.yellow());
            }
            println!();
        }
    }

    /// Create variables in an environment with progress tracking
    pub fn create_variables_with_progress(
        client: &BitbucketClient,
        workspace: &str,
        repo_uuid: &str,
        env_uuid: &str,
        variables: &[VariableValue],
        operation_name: &str,
    ) -> AppResult<()> {
        if variables.is_empty() {
            println!("No variables to create.");
            return Ok(());
        }

        let progress_bar = ProgressBar::new(variables.len() as u64);
        progress_bar.set_style(
            ProgressStyle::default_bar()
                .template(
                    "{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} {msg}",
                )
                .unwrap()
                .progress_chars("#>-"),
        );
        progress_bar.set_message(format!("{} variables", operation_name));

        for variable in variables {
            client.create_variable(workspace, repo_uuid, env_uuid, variable)?;
            progress_bar.set_message(variable.key.clone());
            progress_bar.inc(1);
        }

        progress_bar.finish_with_message(format!("✅ {} completed", operation_name));
        Ok(())
    }

    /// Update variables in an environment with progress tracking
    pub fn update_variables_with_progress(
        client: &BitbucketClient,
        workspace: &str,
        repo_uuid: &str,
        env_uuid: &str,
        variables: &[(String, VariableValue)], // (variable_uuid, variable)
        operation_name: &str,
    ) -> AppResult<()> {
        if variables.is_empty() {
            println!("No variables to update.");
            return Ok(());
        }

        let progress_bar = ProgressBar::new(variables.len() as u64);
        progress_bar.set_style(
            ProgressStyle::default_bar()
                .template(
                    "{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} {msg}",
                )
                .unwrap()
                .progress_chars("#>-"),
        );
        progress_bar.set_message(format!("{} variables", operation_name));

        for (variable_uuid, variable) in variables {
            client.update_variable(workspace, repo_uuid, env_uuid, variable_uuid, variable)?;
            progress_bar.set_message(variable.key.clone());
            progress_bar.inc(1);
        }

        progress_bar.finish_with_message(format!("✅ {} completed", operation_name));
        Ok(())
    }

    /// Convert variables to a HashMap for easier manipulation
    pub fn to_hashmap(variables: &[VariableValue]) -> HashMap<String, VariableValue> {
        variables
            .iter()
            .map(|var| (var.key.clone(), var.clone()))
            .collect()
    }

    /// Find differences between two sets of variables
    pub fn find_differences(
        existing: &[VariableValue],
        new: &[VariableValue],
    ) -> VariableDifferences {
        let existing_map = Self::to_hashmap(existing);
        let new_map = Self::to_hashmap(new);

        let mut to_add = Vec::new();
        let to_update = Vec::new();
        let mut conflicts = Vec::new();

        for (key, new_var) in &new_map {
            if let Some(existing_var) = existing_map.get(key) {
                if existing_var.value != new_var.value {
                    conflicts.push(VariableConflict {
                        key: key.clone(),
                        existing_value: existing_var.value.clone(),
                        new_value: new_var.value.clone(),
                        existing_secured: existing_var.secured,
                        new_secured: new_var.secured,
                    });
                }
            } else {
                to_add.push(new_var.clone());
            }
        }

        VariableDifferences {
            to_add,
            to_update,
            conflicts,
        }
    }

    /// Display variable differences summary
    #[allow(dead_code)]
    pub fn display_differences_summary(differences: &VariableDifferences) {
        if !differences.to_add.is_empty() {
            println!("{} Variables to add:", "✅".green());
            for var in &differences.to_add {
                println!("  + {}", var.key.green());
            }
            println!();
        }

        if !differences.to_update.is_empty() {
            println!("{} Variables to update:", "🔄".yellow());
            for var in &differences.to_update {
                println!("  ~ {}", var.key.yellow());
            }
            println!();
        }

        if !differences.conflicts.is_empty() {
            println!("{} Variable conflicts:", "⚠️".red());
            for conflict in &differences.conflicts {
                println!("  ⚠️  {}", conflict.key.red());
                println!("      Existing: {}", conflict.existing_value);
                println!("      New:      {}", conflict.new_value);
            }
            println!();
        }
    }

    /// Check if a variable name is valid
    fn is_valid_variable_name(name: &str) -> bool {
        if name.is_empty() {
            return false;
        }

        // Must start with letter or underscore
        let first_char = name.chars().next().unwrap();
        if !first_char.is_ascii_alphabetic() && first_char != '_' {
            return false;
        }

        // Rest must be alphanumeric or underscore
        name.chars().all(|c| c.is_ascii_alphanumeric() || c == '_')
    }
}

/// Represents differences between two sets of variables
#[derive(Debug, Clone)]
pub struct VariableDifferences {
    pub to_add: Vec<VariableValue>,
    pub to_update: Vec<VariableValue>,
    pub conflicts: Vec<VariableConflict>,
}

/// Represents a conflict between existing and new variable values
#[derive(Debug, Clone)]
pub struct VariableConflict {
    pub key: String,
    pub existing_value: String,
    pub new_value: String,
    pub existing_secured: bool,
    pub new_secured: bool,
}

impl VariableConflict {
    /// Check if this conflict involves secured variables
    pub fn involves_secured(&self) -> bool {
        self.existing_secured || self.new_secured
    }

    /// Get a display-friendly representation of the conflict
    pub fn display_summary(&self) -> String {
        let secured_indicator = if self.involves_secured() { " 🔒" } else { "" };
        format!(
            "{}{}: '{}' → '{}'",
            self.key, secured_indicator, self.existing_value, self.new_value
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_variable(key: &str, value: &str, secured: bool) -> VariableValue {
        VariableValue {
            type_field: "deployment_variable".to_string(),
            uuid: format!("uuid-{}", key),
            key: key.to_string(),
            value: value.to_string(),
            secured,
        }
    }

    #[test]
    fn test_validate_variables_success() {
        let variables = vec![
            create_test_variable("VAR1", "value1", false),
            create_test_variable("VAR2", "value2", false),
        ];

        assert!(VariableOperations::validate_variables(&variables).is_ok());
    }

    #[test]
    fn test_validate_variables_duplicate_key() {
        let variables = vec![
            create_test_variable("VAR1", "value1", false),
            create_test_variable("VAR1", "value2", false),
        ];

        assert!(VariableOperations::validate_variables(&variables).is_err());
    }

    #[test]
    fn test_filter_secured_variables() {
        let variables = vec![
            create_test_variable("PUBLIC", "value1", false),
            create_test_variable("SECRET", "value2", true),
            create_test_variable("NORMAL", "value3", false),
        ];

        let (secured, unsecured) = VariableOperations::filter_secured_variables(&variables);

        assert_eq!(secured.len(), 1);
        assert_eq!(secured[0].key, "SECRET");
        assert_eq!(unsecured.len(), 2);
    }

    #[test]
    fn test_find_differences() {
        let existing = vec![
            create_test_variable("EXISTING", "old_value", false),
            create_test_variable("UNCHANGED", "same_value", false),
        ];

        let new = vec![
            create_test_variable("EXISTING", "new_value", false), // Conflict
            create_test_variable("UNCHANGED", "same_value", false), // No change
            create_test_variable("NEW_VAR", "new_value", false),  // To add
        ];

        let differences = VariableOperations::find_differences(&existing, &new);

        assert_eq!(differences.to_add.len(), 1);
        assert_eq!(differences.to_add[0].key, "NEW_VAR");
        assert_eq!(differences.conflicts.len(), 1);
        assert_eq!(differences.conflicts[0].key, "EXISTING");
    }

    #[test]
    fn test_find_differences_no_conflicts() {
        let existing = vec![create_test_variable("VAR1", "value1", false)];

        let new = vec![
            create_test_variable("VAR1", "value1", false), // Same value, no conflict
            create_test_variable("VAR2", "value2", false), // New variable
        ];

        let differences = VariableOperations::find_differences(&existing, &new);

        assert_eq!(differences.to_add.len(), 1);
        assert_eq!(differences.to_add[0].key, "VAR2");
        assert_eq!(differences.conflicts.len(), 0);
    }

    #[test]
    fn test_find_differences_empty_lists() {
        let existing = vec![];
        let new = vec![];

        let differences = VariableOperations::find_differences(&existing, &new);

        assert_eq!(differences.to_add.len(), 0);
        assert_eq!(differences.conflicts.len(), 0);
    }

    #[test]
    fn test_find_differences_only_new_variables() {
        let existing = vec![];
        let new = vec![
            create_test_variable("VAR1", "value1", false),
            create_test_variable("VAR2", "value2", false),
        ];

        let differences = VariableOperations::find_differences(&existing, &new);

        assert_eq!(differences.to_add.len(), 2);
        assert_eq!(differences.conflicts.len(), 0);
    }

    #[test]
    fn test_find_differences_secured_variables() {
        let existing = vec![create_test_variable("SECRET", "old_secret", true)];

        let new = vec![create_test_variable("SECRET", "new_secret", true)];

        let differences = VariableOperations::find_differences(&existing, &new);

        assert_eq!(differences.to_add.len(), 0);
        assert_eq!(differences.conflicts.len(), 1);
        assert_eq!(differences.conflicts[0].key, "SECRET");
        assert!(differences.conflicts[0].existing_secured);
        assert!(differences.conflicts[0].new_secured);
    }

    #[test]
    fn test_validate_variables_empty_key() {
        let variables = vec![create_test_variable("", "value", false)];

        let result = VariableOperations::validate_variables(&variables);
        assert!(result.is_err());
    }

    #[test]
    fn test_validate_variables_invalid_key_format() {
        let variables = vec![create_test_variable("123_INVALID", "value", false)];

        let result = VariableOperations::validate_variables(&variables);
        assert!(result.is_err());
    }

    #[test]
    fn test_validate_variables_mixed_valid_invalid() {
        let variables = vec![
            create_test_variable("VALID_VAR", "value1", false),
            create_test_variable("123_INVALID", "value2", false),
        ];

        let result = VariableOperations::validate_variables(&variables);
        assert!(result.is_err());
    }

    #[test]
    fn test_variable_conflict_creation() {
        let existing = create_test_variable("TEST_VAR", "old_value", false);
        let new = create_test_variable("TEST_VAR", "new_value", false);

        let conflict = VariableConflict {
            key: "TEST_VAR".to_string(),
            existing_value: existing.value.clone(),
            new_value: new.value.clone(),
            existing_secured: existing.secured,
            new_secured: new.secured,
        };

        assert_eq!(conflict.key, "TEST_VAR");
        assert_eq!(conflict.existing_value, "old_value");
        assert_eq!(conflict.new_value, "new_value");
    }

    #[test]
    fn test_variable_differences_structure() {
        let to_add = vec![create_test_variable("NEW_VAR", "value", false)];
        let to_update = vec![create_test_variable("UPDATE_VAR", "value", false)];
        let conflicts = vec![VariableConflict {
            key: "CONFLICT_VAR".to_string(),
            existing_value: "old".to_string(),
            new_value: "new".to_string(),
            existing_secured: false,
            new_secured: false,
        }];

        let differences = VariableDifferences {
            to_add: to_add.clone(),
            to_update: to_update.clone(),
            conflicts: conflicts.clone(),
        };

        assert_eq!(differences.to_add.len(), 1);
        assert_eq!(differences.to_update.len(), 1);
        assert_eq!(differences.conflicts.len(), 1);
        assert_eq!(differences.conflicts[0].key, "CONFLICT_VAR");
    }

    #[test]
    fn test_filter_empty_values_no_empty() {
        let variables = vec![
            create_test_variable("VAR1", "value1", false),
            create_test_variable("VAR2", "value2", false),
        ];

        let (valid_vars, skipped_names) =
            VariableOperations::filter_empty_values(variables.clone());

        assert_eq!(valid_vars.len(), 2);
        assert_eq!(skipped_names.len(), 0);
        assert_eq!(valid_vars[0].key, "VAR1");
        assert_eq!(valid_vars[1].key, "VAR2");
    }

    #[test]
    fn test_filter_empty_values_with_empty() {
        let variables = vec![
            create_test_variable("VAR1", "value1", false),
            create_test_variable("EMPTY_VAR", "", false),
            create_test_variable("VAR2", "value2", false),
            create_test_variable("WHITESPACE_VAR", "   ", false),
        ];

        let (valid_vars, skipped_names) = VariableOperations::filter_empty_values(variables);

        assert_eq!(valid_vars.len(), 2);
        assert_eq!(skipped_names.len(), 2);
        assert_eq!(valid_vars[0].key, "VAR1");
        assert_eq!(valid_vars[1].key, "VAR2");
        assert!(skipped_names.contains(&"EMPTY_VAR".to_string()));
        assert!(skipped_names.contains(&"WHITESPACE_VAR".to_string()));
    }

    #[test]
    fn test_filter_empty_values_all_empty() {
        let variables = vec![
            create_test_variable("EMPTY1", "", false),
            create_test_variable("EMPTY2", "   ", false),
        ];

        let (valid_vars, skipped_names) = VariableOperations::filter_empty_values(variables);

        assert_eq!(valid_vars.len(), 0);
        assert_eq!(skipped_names.len(), 2);
        assert!(skipped_names.contains(&"EMPTY1".to_string()));
        assert!(skipped_names.contains(&"EMPTY2".to_string()));
    }

    #[test]
    fn test_validate_variables_empty_value_error() {
        let variables = vec![
            create_test_variable("VAR1", "value1", false),
            create_test_variable("EMPTY_VAR", "", false),
        ];

        let result = VariableOperations::validate_variables(&variables);
        assert!(result.is_err());

        if let Err(AppError::Validation(ValidationError::InvalidVariableValue(msg))) = result {
            assert!(msg.contains("EMPTY_VAR"));
            assert!(msg.contains("empty value"));
        } else {
            panic!("Expected InvalidVariableValue error");
        }
    }
}
