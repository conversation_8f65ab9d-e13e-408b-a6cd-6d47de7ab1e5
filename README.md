# BitBucket Environment Variable Manager

A comprehensive tool for managing BitBucket deployment environment variables with support for multiple workflows:

1. **Environment-to-Environment Copy**: Copy variables from one BitBucket environment to another
2. **File-to-New-Environment**: Create a new BitBucket environment populated with variables from a .env file
3. **File-to-Existing-Environment**: Append/merge variables from a .env file into an existing BitBucket environment with conflict resolution

The user is guided through an interactive process for each workflow.

## Authentication

This tool uses **HTTP Basic Authentication** with the Bitbucket API. You need:

1. **Username**: Your Atlassian account email address
2. **API Token**: A Bitbucket API token (not your password)

### Creating a Bitbucket API Token

1. Go to your Bitbucket account settings
2. Navigate to "App passwords" or "API tokens"
3. Create a new token with appropriate permissions for repository access
4. Use this token as the API token for authentication

## Usage

The tool supports three distinct workflows. You can specify the workflow using command line arguments or be guided through an interactive selection process.

### Command Line Arguments
```
Usage: bb-copy-env [OPTIONS] [WORKFLOW]

Options:
  -u, --username <USERNAME>        Atlassian account email address (username for Basic Auth)
  -t, --api-token <API_TOKEN>      BitBucket API token (password for Basic Auth)
  -w, --workspace <WORKSPACE>      A BitBucket Workspace [default: technofarm]
  -v, --verbose                    Show verbose output
  -h, --help                       Print help information

Workflows:
  env-to-env                       Copy variables from one environment to another
  file-to-new <FILE>              Create new environment from .env file
  file-to-existing <FILE> [STRATEGY]  Append variables to existing environment
                                   STRATEGY: overwrite, skip, prompt (default: prompt)
```

### Workflow Examples

#### 1. Environment-to-Environment Copy
Copy all variables from one deployment environment to another:

```bash
# Interactive mode (recommended)
bb-copy-env env-to-env

# With credentials
bb-copy-env --username "<EMAIL>" --api-token "your-token" env-to-env
```

#### 2. File-to-New-Environment
Create a new BitBucket environment populated with variables from a .env file:

```bash
# Create new environment from .env file
bb-copy-env file-to-new .env

# With specific workspace
bb-copy-env --workspace "my-workspace" file-to-new production.env
```

#### 3. File-to-Existing-Environment
Append/merge variables from a .env file into an existing environment:

```bash
# Interactive conflict resolution (default)
bb-copy-env file-to-existing .env

# Automatically overwrite conflicts
bb-copy-env file-to-existing .env overwrite

# Skip conflicting variables
bb-copy-env file-to-existing .env skip

# Prompt for each conflict (explicit)
bb-copy-env file-to-existing .env prompt
```

### Environment Variables (Recommended)

For security, it's recommended to use environment variables instead of command line arguments:

```bash
export BITBUCKET_USERNAME="<EMAIL>"
export BITBUCKET_API_TOKEN="your-api-token"

# Run any workflow
bb-copy-env env-to-env
bb-copy-env file-to-new .env
bb-copy-env file-to-existing .env overwrite
```

### Mixed Usage

You can also mix command line arguments and environment variables:

```bash
export BITBUCKET_API_TOKEN="your-api-token"
bb-copy-env --username "<EMAIL>" file-to-new .env
```

### Interactive Mode

If you run the tool without specifying a workflow, you'll be guided through an interactive selection:

```bash
bb-copy-env
# Will prompt you to choose between:
# 1. Environment to Environment Copy
# 2. File to New Environment
# 3. File to Existing Environment
```

## .env File Format

The tool supports standard .env file format for file-based workflows:

```bash
# Comments are supported
DATABASE_URL=postgresql://localhost:5432/mydb
API_KEY=your-secret-api-key
DEBUG=true

# Quoted values are supported
MESSAGE="Hello, World!"
PATH='/usr/local/bin:/usr/bin'

# Empty values are allowed
OPTIONAL_VAR=

# Sensitive variables are automatically detected and marked as secured
SECRET_KEY=my-secret-key
PASSWORD=my-password
API_TOKEN=token-value
PRIVATE_KEY=private-key-content
```

### Sensitive Variable Detection

The tool automatically detects sensitive variables based on common naming patterns and marks them as secured in BitBucket:

- Variables containing: `KEY`, `SECRET`, `PASSWORD`, `TOKEN`, `PRIVATE`, `AUTH`, `CREDENTIAL`
- Case-insensitive matching
- Secured variables are hidden in the BitBucket UI

## Conflict Resolution

When using the `file-to-existing` workflow, conflicts may occur when variables already exist in the target environment. The tool provides three resolution strategies:

### 1. Prompt (Default)
Interactive resolution for each conflict:
```bash
bb-copy-env file-to-existing .env prompt
```
- Shows existing and new values
- Allows you to choose per variable: overwrite, skip, or view details

### 2. Overwrite
Automatically overwrite all conflicting variables:
```bash
bb-copy-env file-to-existing .env overwrite
```
- Replaces existing values with new ones
- No user interaction required
- Use with caution in production environments

### 3. Skip
Skip all conflicting variables, only add new ones:
```bash
bb-copy-env file-to-existing .env skip
```
- Preserves existing values
- Only adds variables that don't already exist
- Safe for production environments

## Build
To build the project you need a local installation of Rust and to execute the command below. After that the executable will be in `target/release/`.

```bash
cargo build --release
```

## Run
To build and run execute the command below:

```bash
cargo run
```

## Testing
Run the comprehensive test suite:

```bash
# Run all tests
cargo test

# Run tests with output
cargo test -- --nocapture

# Run specific test module
cargo test bitbucket_client
cargo test env_file_parser
cargo test workflows
```

## Security Best Practices

1. **Never commit credentials to version control**
2. **Use environment variables** instead of command line arguments when possible
3. **Rotate API tokens regularly**
4. **Use minimal permissions** when creating API tokens
5. **Store tokens securely** (e.g., in a password manager or secure environment)

## Docker

The command can be run from a Docker container. Use environment variables for secure credential passing:

```bash
# Build the image
docker build -t bb-copy-env .

# Run with environment variables (recommended)
docker run --rm -it \
  -e BITBUCKET_USERNAME="<EMAIL>" \
  -e BITBUCKET_API_TOKEN="your-api-token" \
  bb-copy-env env-to-env

# Run file-to-new workflow with volume mount
docker run --rm -it \
  -e BITBUCKET_USERNAME="<EMAIL>" \
  -e BITBUCKET_API_TOKEN="your-api-token" \
  -v $(pwd):/app/files \
  bb-copy-env file-to-new /app/files/.env

# Run file-to-existing workflow with conflict resolution
docker run --rm -it \
  -e BITBUCKET_USERNAME="<EMAIL>" \
  -e BITBUCKET_API_TOKEN="your-api-token" \
  -v $(pwd):/app/files \
  bb-copy-env file-to-existing /app/files/.env overwrite

# Interactive mode
docker run --rm -it \
  -e BITBUCKET_USERNAME="<EMAIL>" \
  -e BITBUCKET_API_TOKEN="your-api-token" \
  bb-copy-env
```

## Architecture

The application follows a modular architecture with clear separation of concerns:

### Core Components

- **BitbucketClient**: HTTP client for BitBucket API v2.0 interactions using repository UUIDs
- **EnvFileParser**: Parser for .env file format with sensitive variable detection
- **ConflictResolver**: Handles variable conflicts with multiple resolution strategies
- **VariableOperations**: Core logic for variable validation and difference analysis
- **Workflows**: Three distinct workflow implementations with shared interfaces

### API Implementation

The tool follows Bitbucket Cloud REST API best practices by using repository UUIDs instead of repository names/slugs for all API calls. Repository UUIDs follow the format `{xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}` (including curly braces) and ensure consistent identification regardless of repository name changes.

### Workflow System

The application uses a factory pattern to create workflow instances:

- **EnvToEnvWorkflow**: Handles environment-to-environment variable copying
- **FileToNewEnvWorkflow**: Creates new environments from .env files
- **FileToExistingEnvWorkflow**: Merges .env files into existing environments

### Error Handling

Comprehensive error handling with specific error types:

- **ApiError**: BitBucket API-related errors (authentication, rate limiting, etc.)
- **FileIoError**: File system operations and parsing errors
- **ValidationError**: Input validation and data integrity errors
- **WorkflowError**: Workflow-specific errors and user cancellation
- **ConflictError**: Variable conflict resolution errors

### Testing

The codebase includes comprehensive unit and integration tests:

- **49 test cases** covering all major functionality
- **Mock-based testing** for external API interactions
- **Temporary file testing** for file operations
- **Edge case coverage** for error conditions and validation