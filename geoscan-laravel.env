# Environment variables for geoscan laravel project
# Generated from project-envs.md

BG_DB_PASSWORD=6nuk23
MAIN_DB_PASSWORD=6nuk23
RO_DB_PASSWORD=6nuk23
RS_DB_PASSWORD=6nuk23
TR_DB_PASSWORD=6nuk23
ZA_DB_PASSWORD=6nuk23
UA_DB_PASSWORD=6nuk23
KZ_DB_PASSWORD=6nuk23
IT_DB_PASSWORD=6nuk23
ES_DB_PASSWORD=6nuk23
GEOSCAN_DB_PASSWORD=
KEYCLOAK_CLIENT_SECRET=dIXqCWZ5kVeN6Pwt4nFkcjuuW4IXorKj
KEYCLOAK_PASSWORD_GRANT_CLIENT_SECRET=i0cxhmguUDdOdzPeYeUDuNlbeBDjtiYn
KEYCLOAK_M2M_CLIENT_SECRET=YeCSHq4vabdL7gvyFeXDlg9qYu6VeJwk
MYSQL_DB_PASSWORD=3jRYs4b9cM^j
KUBE_TOKEN=
KUBE_CA=
STATIC_MAP_API_KEY=ApmGDBK5yjcRxRel0ArlFpEd0SsQbeMDNe96UZxJZkytrMzMQjCwhsKuavL8eWCo
EODATA_SECRET_ACCESS_KEY=H5vsOIEGs2BEsmeKeqwcsEnFwQQ6SEjEwdlXVyAx
