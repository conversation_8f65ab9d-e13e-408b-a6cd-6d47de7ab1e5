# Environment variables exported from BitBucket
# Workspace: technofarm
# Repository: TechnoFarm
# Environment: Agrimi
# Export timestamp: 1751987973
# Total variables: 74
# Secured variables skipped: 8
#
# WARNING: This file may contain sensitive information.
# Do not commit this file to version control.

# The following secured variables were skipped during export:
# DEFAULT_DB_PASSWORD (secured variable - value not exported)
# DBLINK_PASSWORD (secured variable - value not exported)
# RPI_PASSWORD (secured variable - value not exported)
# PHPMAILER_PASSWORD (secured variable - value not exported)
# KUBE_TOKEN (secured variable - value not exported)
# KUBE_CA (secured variable - value not exported)
# POSTHOG_API_KEY (secured variable - value not exported)
# KEYKLOACK_KVS_STORE_CLIENT_SECRET (secured variable - value not exported)

DEFAULT_DB_USERNAME=postgres
DEFAULT_DB_HOST=hippo-ha-primary.crunchy-pgsql.svc
DEFAULT_DB_PORT=5432
DEFAULT_DB_DATABASE=susi_main
DBLINK_DRIVER=pgsql
DBLINK_HOST=hippo-ha-primary.crunchy-pgsql.svc
DBLINK_PORT=5432
DBLINK_DATABASE=susi_main
DBLINK_USERNAME=postgres
WMS_SERVER=https://mapserver-proxy.agrimi.com?proxy=http://tf-technofarm-mapserver.agrimi.svc:6005/mapserv
LOGIN3_WMS_SERVER=https://mapserver-proxy.agrimi.com?proxy=http://tf-technofarm-mapcache.agrimi.svc:6004/mapcache
RPI_SERVER=http://tf-connect-nginx
RPI_USERNAME=tfqa
SITE_URL=https://app.agrimi.com/tf/
PHPMAILER_HOST=smtp.gmail.com
PHPMAILER_PORT=587
TRIMBLE_EXPORT_URL=http://tf-common-services-nginx/export/trimble
WAREHOUSE_API_URL=http://tf-warehouse-nginx:8081
CONFIGMAP=tf-technofarm
REDIS_HOST=tf-redis
REDIS_PORT=6379
CONFIGMAPFILES=tf-technofarm-files
NFS_SERVER=**************
WMS_SERVER_INTERNAL=http://tf-technofarm-mapserver:6005/mapserv
NAMESPACE=agrimi
NFS_DIR_MAPS=/data/technofarm-nfs/maps
NFS_DIR_FILES=/data/technofarm-nfs/tf-files
NFS_DIR_LOGS=/data/technofarm-nfs/tf-logs
NFS_DIR_STATIC=/data/technofarm-nfs/static_maps
NFS_DIR_MAPCACHE=/data/technofarm-nfs/mapcache-config
KUBE_CLUSTER=kubernetes
KUBE_SERVER=https://**************:6443
KUBE_SA=bitbucket
KEYKLOACK_LOGIN_REQUIRED=true
KEYCLOAK_AUTH_SERVER_URL=https://id.agrimi.com
KEYCLOAK_REALM=k8s
KEYCLOAK_CLIENT_ID=agrimi-technofarm-backend
KEYCLOAK_CLIENT_SECRET=RGafTOR4KaAM2Hez44jJYhDEGFUkb8ki
KEYCLOAK_REDIRECT_URI=https://app.agrimi.com/tf/index.php?keycloak=KeycloakLogin
KEYCLOAK_LOGOUT_REDIRECT_URI=https://app.agrimi.com/tf/
KEYCLOAK_ALGORYTHM=RS256
CMS_API_URL=https://cms-api.agrimi.com
SITE_BASE_HREF=/tf/
COMMON_SERVICES_API_URL=http://tf-common-services-nginx
PUBLIC_PATH_PREFIX=/tf
KAIS_SESSION_ID=x2lwzykicof0ejes4oya3aml
KAIS_CA_TOKEN=ZWVpQmJYb3JWRFhRcWFsNW5tZTZYSTArUG9udHhRZ1RLSU9BcmVURkNFUXBLaGxnRzdBc2E3Z3dSWXJFTFpqWjdKTU1mOSt5bWI1SSszRFhuRGZOSXRjYTdOR1hJUGd6bndhM1kyMFo3L2xFUjFIWHFvUm83cXpmbkZPYUNjaUNRN09pWGZKT2pNST0=
APP_ENV=prod
HELPHERO_ID=wGITfg4KDG
GEOSCAN_APP_URL=https://app.agrimi.com
MAPCACHE_CONFIGMAP=tf-mapcache
MAPCACHE_CACHE_DIR=/var/sig/tiles/.layers/
PHPMAILER_USERNAME=<EMAIL>
MAIN_NAVIGATION_INSTANCE=web
LEGACY_MODE=false
KEYKLOACK_KVS_STORE_AUTH_SERVER_URL=https://id.agrimi.com
KEYKLOACK_KVS_STORE_CLIENT_ID=technofarm-app
GEOSCAN_CMS_BASE_URI=http://geoscan-cms-api-prod-nginx/
KVS_STORE_URL=http://kvs-store-api-nginx/
MS_MAP_PATTERN="^\\/var\\/www\\/html\\/app\\/(static_)?maps\\/([^\\.][-_A-Za-z0-9\\.]+\\/{1})*([-_A-Za-z0-9\\.]+\\.map)$$"
MS_DEBUGLEVEL=0
APACHE_RUN_USER=appuser
APACHE_RUN_GROUP=appgroup
MAPSERVER_CATCH_SEGV=0
MIN_PROCESSES=1
MAX_PROCESSES=5
NGINX_CONFIGMAP=tf-technofarm-nginx
KEYKLOACK_KVS_STORE_REALM=kvs-store
CONTAINER_NAME=tf-technofarm
KVS_STORE_CALLBACK_URL=http://tf-technofarm-nginx:8080/index.php?
CSV2XLS_PATH=/usr/local/bin/
MAX_KVS_PROCESSING_FILES=10
ALARMS_MAIL=<EMAIL>
KUBE_REPLICAS=2
