[package]
name = "bb-copy-env"
version = "1.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
reqwest = { version = "0.11", features = ["json", "blocking"] }
tokio = { version = "1", features = ["full"] }
serde = "1"
serde_json = "1.0"
serde_with = "2.1.0"
inquire = "0.5.2"
clap = { version = "4.0", features = ["derive", "env"] }
colored = "2.0.0"
indicatif = "0.17.5"
anyhow = "1.0.75"
base64 = "0.21"

[dev-dependencies]
serde_derive = "1.0"
mockito = "1.2"
tempfile = "3.8"
assert_matches = "1.5"
